import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin } from '@/lib/auth'

// GET /api/admin/benefit-rankings - Get ranking analytics and statistics
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const benefitId = searchParams.get('benefitId')
    const includeUserDetails = searchParams.get('includeUserDetails') === 'true'

    if (benefitId) {
      // Get rankings for a specific benefit
      let sql = `
        SELECT
          ubr.id,
          ubr.user_id,
          ubr.benefit_id,
          ubr.ranking,
          ubr.created_at,
          ubr.updated_at,
          b.name as benefit_name,
          bc.name as category,
          b.icon
      `

      if (includeUserDetails) {
        sql += `,
          u.email as user_email,
          u.first_name,
          u.last_name
        `
      }

      sql += `
        FROM user_benefit_rankings ubr
        LEFT JOIN benefits b ON ubr.benefit_id = b.id
        LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      `

      if (includeUserDetails) {
        sql += `LEFT JOIN users u ON ubr.user_id = u.id`
      }

      sql += `
        WHERE ubr.benefit_id = $1
        ORDER BY ubr.ranking ASC, ubr.created_at ASC
      `

      const result = await query(sql, [benefitId])

      return NextResponse.json({
        benefitId,
        rankings: result.rows,
        total: result.rows.length
      })
    }

    // Get overall ranking statistics
    const statsResult = await query(`
      SELECT
        b.id as benefit_id,
        b.name as benefit_name,
        bc.name as category,
        b.icon,
        COUNT(ubr.id) as total_rankings,
        AVG(ubr.ranking::numeric) as average_ranking,
        MIN(ubr.ranking) as best_ranking,
        MAX(ubr.ranking) as worst_ranking,
        COUNT(CASE WHEN ubr.ranking <= 3 THEN 1 END) as top_3_count,
        COUNT(CASE WHEN ubr.ranking >= 8 THEN 1 END) as bottom_3_count
      FROM benefits b
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      LEFT JOIN user_benefit_rankings ubr ON b.id = ubr.benefit_id
      GROUP BY b.id, b.name, bc.name, b.icon
      HAVING COUNT(ubr.id) > 0
      ORDER BY average_ranking ASC NULLS LAST, total_rankings DESC
    `)

    // Get ranking distribution
    const distributionResult = await query(`
      SELECT 
        ranking,
        COUNT(*) as count
      FROM user_benefit_rankings
      GROUP BY ranking
      ORDER BY ranking ASC
    `)

    // Get most active users (users with most rankings)
    const activeUsersResult = await query(`
      SELECT 
        u.id,
        u.email,
        u.first_name,
        u.last_name,
        COUNT(ubr.id) as ranking_count,
        MAX(ubr.updated_at) as last_ranking_update
      FROM users u
      JOIN user_benefit_rankings ubr ON u.id = ubr.user_id
      GROUP BY u.id, u.email, u.first_name, u.last_name
      ORDER BY ranking_count DESC, last_ranking_update DESC
      LIMIT 20
    `)

    return NextResponse.json({
      benefitStats: statsResult.rows,
      rankingDistribution: distributionResult.rows,
      activeUsers: activeUsersResult.rows,
      totalBenefitsRanked: statsResult.rows.length,
      totalRankings: distributionResult.rows.reduce((sum, row) => sum + parseInt(row.count), 0)
    })

  } catch (error) {
    console.error('Error fetching ranking analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch ranking analytics' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/benefit-rankings - Reset rankings for a specific benefit
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const benefitId = searchParams.get('benefitId')

    if (!benefitId) {
      return NextResponse.json(
        { error: 'benefitId parameter is required' },
        { status: 400 }
      )
    }

    // Verify benefit exists
    const benefitCheck = await query(
      'SELECT name FROM benefits WHERE id = $1',
      [benefitId]
    )

    if (benefitCheck.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit not found' },
        { status: 404 }
      )
    }

    const benefitName = benefitCheck.rows[0].name

    // Delete all rankings for this benefit
    const result = await query(
      'DELETE FROM user_benefit_rankings WHERE benefit_id = $1',
      [benefitId]
    )

    return NextResponse.json({
      success: true,
      message: `All rankings for "${benefitName}" have been reset`,
      deletedCount: result.rowCount,
      benefitId,
      benefitName
    })

  } catch (error) {
    console.error('Error resetting benefit rankings:', error)
    return NextResponse.json(
      { error: 'Failed to reset benefit rankings' },
      { status: 500 }
    )
  }
}
