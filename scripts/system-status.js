#!/usr/bin/env node

// System Status Script - Shows complete status of database schema and cache system
// Provides comprehensive overview of the PostgreSQL-based caching system

import { config } from 'dotenv'
import { query } from '../src/lib/local-db.ts'
import { getCacheStats } from '../src/lib/postgresql-cache.ts'

// Load environment variables
config({ path: '.env.local' })

async function checkDatabaseSchema() {
  console.log('🗄️  Database Schema Status:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

  try {
    // Check companies table columns
    const companiesColumns = await query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'companies'
      ORDER BY ordinal_position
    `)

    console.log('📊 Companies table columns:')
    const expectedColumns = ['website', 'logo_url', 'founded_year', 'verification_status']
    expectedColumns.forEach(col => {
      const exists = companiesColumns.rows.some(row => row.column_name === col)
      console.log(`   ${exists ? '✅' : '❌'} ${col}`)
    })

    // Check materialized views
    const views = await query(`
      SELECT schemaname, matviewname
      FROM pg_matviews
      WHERE matviewname LIKE '%cache%'
    `)

    console.log('\n📈 Materialized Views:')
    views.rows.forEach(view => {
      console.log(`   ✅ ${view.matviewname}`)
    })

    // Check cache functions
    const functions = await query(`
      SELECT proname
      FROM pg_proc
      WHERE proname LIKE '%cache%' OR proname LIKE '%session%'
    `)

    console.log('\n⚙️  Cache Functions:')
    functions.rows.forEach(func => {
      console.log(`   ✅ ${func.proname}()`)
    })

  } catch (error) {
    console.error('❌ Database schema check failed:', error.message)
  }
}

async function checkCacheData() {
  console.log('\n🗄️  Cache Data Status:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

  try {
    // Check materialized view data
    const companiesCount = await query('SELECT COUNT(*) as count FROM companies_with_benefits_cache')
    const benefitsCount = await query('SELECT COUNT(*) as count FROM benefits_with_categories_cache')

    console.log(`📊 Companies with benefits cache: ${companiesCount.rows[0].count} entries`)
    console.log(`🎁 Benefits with categories cache: ${benefitsCount.rows[0].count} entries`)

    // Get cache statistics
    const cacheStats = await getCacheStats()
    console.log(`💾 Function cache entries: ${cacheStats.totalCacheEntries}`)
    console.log(`⏰ Expired cache entries: ${cacheStats.expiredEntries}`)

  } catch (error) {
    console.error('❌ Cache data check failed:', error.message)
  }
}

async function checkMigrationStatus() {
  console.log('\n📋 Migration Status:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

  try {
    const migrations = await query(`
      SELECT migration_name, description, applied_at
      FROM migration_log
      ORDER BY applied_at DESC
    `)

    migrations.rows.forEach(migration => {
      console.log(`   ✅ ${migration.migration_name}`)
      console.log(`      ${migration.description}`)
      console.log(`      Applied: ${new Date(migration.applied_at).toLocaleString()}`)
      console.log('')
    })

  } catch (error) {
    console.error('❌ Migration status check failed:', error.message)
  }
}

async function main() {
  console.log('🔍 BenefitLens System Status Check\n')

  await checkDatabaseSchema()
  await checkCacheData()
  await checkMigrationStatus()

  console.log('🎉 System status check completed!')
  process.exit(0)
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️  System status check interrupted')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️  System status check terminated')
  process.exit(1)
})

main()