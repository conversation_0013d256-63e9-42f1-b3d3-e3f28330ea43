#!/usr/bin/env node

/**
 * Email Configuration Test Script
 * 
 * This script tests your email configuration by sending a test email.
 * Usage: node scripts/test-email.js <recipient-email>
 */

require('dotenv').config({ path: '.env.local' })

const { sendEmail } = require('../src/lib/email.ts')

async function testEmail() {
  const recipientEmail = process.argv[2]
  
  if (!recipientEmail) {
    console.error('Usage: node scripts/test-email.js <recipient-email>')
    process.exit(1)
  }

  console.log('Testing email configuration...')
  console.log(`SMTP Host: ${process.env.SMTP_HOST}`)
  console.log(`SMTP Port: ${process.env.SMTP_PORT}`)
  console.log(`From Email: ${process.env.FROM_EMAIL}`)
  console.log(`Environment: ${process.env.NODE_ENV}`)
  console.log(`Recipient: ${recipientEmail}`)
  console.log('---')

  try {
    const result = await sendEmail({
      to: recipientEmail,
      subject: 'BenefitLens Email Test',
      html: `
        <h2>Email Configuration Test</h2>
        <p>This is a test email from BenefitLens to verify your email configuration.</p>
        <p><strong>Configuration Details:</strong></p>
        <ul>
          <li>SMTP Host: ${process.env.SMTP_HOST}</li>
          <li>SMTP Port: ${process.env.SMTP_PORT}</li>
          <li>Environment: ${process.env.NODE_ENV}</li>
          <li>Timestamp: ${new Date().toISOString()}</li>
        </ul>
        <p>If you received this email, your configuration is working correctly!</p>
      `,
      text: `
        BenefitLens Email Configuration Test
        
        This is a test email to verify your email configuration.
        
        Configuration Details:
        - SMTP Host: ${process.env.SMTP_HOST}
        - SMTP Port: ${process.env.SMTP_PORT}
        - Environment: ${process.env.NODE_ENV}
        - Timestamp: ${new Date().toISOString()}
        
        If you received this email, your configuration is working correctly!
      `
    })

    console.log('✅ Email sent successfully!')
    console.log('Message ID:', result.messageId)
    
    if (process.env.NODE_ENV === 'development') {
      console.log('\n📧 Check MailHog at http://localhost:8025 to see the email')
    }
    
  } catch (error) {
    console.error('❌ Email test failed:', error.message)
    process.exit(1)
  }
}

testEmail()
