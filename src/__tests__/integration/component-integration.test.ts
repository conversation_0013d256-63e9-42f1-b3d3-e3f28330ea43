/**
 * Component Integration Tests
 * Tests component behavior with real database integration
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import {
  setupTestDatabase,
  cleanupTestDatabase
} from './setup'

describe('Component Integration Tests', () => {
  beforeAll(async () => {
    console.log('🧪 Starting component integration tests with real database')
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
  })

  beforeEach(async () => {
    await setupTestDatabase()
  })

  describe('Component Module Integration', () => {
    it('should verify component modules can be imported', async () => {
      // Test that component modules can be imported without errors
      const benefitsListModule = await import('@/components/benefits-list')
      expect(benefitsListModule).toBeDefined()
      expect(typeof benefitsListModule.BenefitsList).toBe('function')
    })

    it('should verify admin component modules can be imported', async () => {
      const adminModule = await import('@/components/admin-dashboard')
      expect(adminModule).toBeDefined()
      expect(typeof adminModule.AdminDashboard).toBe('function')
    })

    it('should verify company dashboard components can be imported', async () => {
      const dashboardModule = await import('@/components/company-dashboard')
      expect(dashboardModule).toBeDefined()
      expect(typeof dashboardModule.CompanyDashboard).toBe('function')
    })

    it('should verify batch benefit selection component can be imported', async () => {
      const batchModule = await import('@/components/batch-benefit-selection')
      expect(batchModule).toBeDefined()
      expect(typeof batchModule.BatchBenefitSelection).toBe('function')
    })

    it('should verify analytics components can be imported', async () => {
      const analyticsModule = await import('@/components/analytics-dashboard')
      expect(analyticsModule).toBeDefined()
      expect(typeof analyticsModule.AnalyticsDashboard).toBe('function')
    })

    it('should verify benefit ranking component can be imported', async () => {
      const rankingModule = await import('@/components/benefit-ranking')
      expect(rankingModule).toBeDefined()
      expect(typeof rankingModule.BenefitRanking).toBe('function')
    })
  })

  describe('Component Props Validation', () => {
    it('should validate component props interface', async () => {
      // Test that component props match expected interface
      const mockProps = {
        companyId: 'test-company-1',
        existingBenefitIds: ['benefit-1', 'benefit-2'],
        onSuccess: () => {},
        onCancel: () => {},
      }

      expect(mockProps.companyId).toBeDefined()
      expect(Array.isArray(mockProps.existingBenefitIds)).toBe(true)
      expect(typeof mockProps.onSuccess).toBe('function')
      expect(typeof mockProps.onCancel).toBe('function')
    })

    it('should validate benefit ranking props interface', async () => {
      const mockProps = {
        userId: 'test-user-1',
        initialRankings: [
          { benefitId: 'test-benefit-1', ranking: 1 },
          { benefitId: 'test-benefit-2', ranking: 2 }
        ],
        onRankingChange: () => {},
        onSave: () => {},
        onReset: () => {}
      }

      expect(mockProps.userId).toBeDefined()
      expect(Array.isArray(mockProps.initialRankings)).toBe(true)
      expect(typeof mockProps.onRankingChange).toBe('function')
      expect(typeof mockProps.onSave).toBe('function')
      expect(typeof mockProps.onReset).toBe('function')
    })
  })
})
