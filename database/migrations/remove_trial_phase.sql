-- Migration: Remove trial phase - only free and paying status allowed
-- This migration removes trial support and updates existing trial users to free

-- Update existing trial users to free status
UPDATE users SET payment_status = 'free' WHERE payment_status = 'trial';

-- Drop the trial_expires_at column
ALTER TABLE users DROP COLUMN IF EXISTS trial_expires_at;

-- Update the check constraint to only allow 'free' and 'paying'
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_payment_status_check;
ALTER TABLE users ADD CONSTRAINT users_payment_status_check CHECK (payment_status IN ('free', 'paying'));

-- Drop the trial expiration index
DROP INDEX IF EXISTS idx_users_trial_expires_at;
