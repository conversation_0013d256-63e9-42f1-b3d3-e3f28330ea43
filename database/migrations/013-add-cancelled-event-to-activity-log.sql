-- Migration: Add 'benefit_removal_dispute_cancelled' event type to activity log
-- Date: 2025-08-01

-- Drop the existing constraint
ALTER TABLE activity_log 
DROP CONSTRAINT activity_log_event_type_check;

-- Add the new constraint with the cancelled event type
ALTER TABLE activity_log 
ADD CONSTRAINT activity_log_event_type_check 
CHECK (event_type IN (
  'company_added',
  'user_registered', 
  'benefit_verified',
  'benefit_disputed',
  'benefit_removal_dispute_submitted',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_cancelled',
  'benefit_automatically_removed'
));

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('013-add-cancelled-event-to-activity-log', 'Add benefit_removal_dispute_cancelled event type to activity log constraint');
