'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  paymentStatus: string
  company_domain?: string
  is_premium?: boolean
}

interface AdminPageWrapperProps {
  children: React.ReactNode
}

export function AdminPageWrapper({ children }: AdminPageWrapperProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    async function checkAuth() {
      try {
        console.log('AdminPageWrapper: Checking authentication...')
        const response = await fetch('/api/auth/me')
        console.log('AdminPageWrapper: Auth response status:', response.status)

        if (!response.ok) {
          if (response.status === 401) {
            console.log('AdminPageWrapper: Not authenticated, redirecting to sign-in')
            router.push('/sign-in')
            return
          }
          throw new Error('Failed to check authentication')
        }

        const data = await response.json()
        const userData = data.user
        console.log('AdminPageWrapper: User data:', userData)

        if (!userData) {
          console.log('AdminPageWrapper: No user data, redirecting to sign-in')
          router.push('/sign-in')
          return
        }

        // Check if user has admin role
        if (userData.role !== 'admin') {
          console.log('AdminPageWrapper: User is not admin, redirecting to home')
          router.push('/')
          return
        }

        console.log('AdminPageWrapper: Admin user verified, setting user state')
        setUser(userData)
      } catch (err) {
        console.error('AdminPageWrapper: Authentication check failed:', err)
        setError('Failed to verify authentication')
        // On error, redirect to sign-in after a delay
        setTimeout(() => {
          router.push('/sign-in')
        }, 2000)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying admin access...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-red-600 font-medium">{error}</p>
          <p className="text-gray-600 mt-2">Redirecting to sign-in...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
