import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logger } from '@/lib/logger'

/**
 * DELETE /api/user/saved-companies/[companyId]
 * Remove a saved company for the current user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { companyId } = await params

    // Remove the saved company
    const result = await query(
      'DELETE FROM saved_companies WHERE user_id = $1 AND company_id = $2 RETURNING id',
      [user.id, companyId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Saved company not found' },
        { status: 404 }
      )
    }

    logger.info('Company unsaved by user', {
      userId: user.id,
      companyId,
      removedId: result.rows[0].id
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    logger.error('Error removing saved company for user', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
