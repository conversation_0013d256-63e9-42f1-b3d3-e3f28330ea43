#!/usr/bin/env node

// Cache refresh script for PostgreSQL-based caching system
// Refreshes materialized views and clears expired cache entries

import { config } from 'dotenv'
import { refreshCacheViews, scheduledCacheMaintenance } from '../src/lib/postgresql-cache.ts'

// Load environment variables
config({ path: '.env.local' })

async function main() {
  console.log('🔄 Starting cache refresh...\n')
  
  try {
    console.log('📊 Refreshing materialized views...')
    await refreshCacheViews()
    console.log('✅ Materialized views refreshed')
    
    console.log('\n🧹 Running cache maintenance...')
    await scheduledCacheMaintenance()
    console.log('✅ Cache maintenance completed')
    
    console.log('\n🎉 Cache refresh completed successfully')
    process.exit(0)
  } catch (error) {
    console.error('❌ Cache refresh failed:', error.message)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️  Cache refresh interrupted')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️  Cache refresh terminated')
  process.exit(1)
})

main()
