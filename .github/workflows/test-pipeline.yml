name: BenefitLens Test Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
  NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
  NEXTAUTH_URL: http://localhost:3000

jobs:
  # Job 1: Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript check
      run: npx tsc --noEmit
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Run unit tests
      run: npm run test:unit -- --run --coverage --reporter=json --outputFile=unit-results.json
      
    - name: Upload unit test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: unit-test-results
        path: |
          unit-results.json
          coverage/
          
    - name: Comment PR with unit test results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          if (fs.existsSync('unit-results.json')) {
            const results = JSON.parse(fs.readFileSync('unit-results.json', 'utf8'));
            const comment = `## 🧪 Unit Test Results
            
            - **Tests**: ${results.numTotalTests}
            - **Passed**: ${results.numPassedTests}
            - **Failed**: ${results.numFailedTests}
            - **Duration**: ${results.testResults?.[0]?.perfStats?.runtime || 'N/A'}ms
            
            ${results.numFailedTests > 0 ? '❌ Some unit tests failed' : '✅ All unit tests passed'}`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }

  # Job 2: Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: benefitlens_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup test database
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/benefitlens_test
      run: |
        npm run db:migrate
        npm run db:seed
        
    - name: Build application
      run: npm run build
      
    - name: Run integration tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/benefitlens_test
        TEST_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/benefitlens_test
      run: npm run test:integration -- --run --reporter=json --outputFile=integration-results.json
      
    - name: Upload integration test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: integration-results.json

  # Job 3: E2E Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: benefitlens_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
      
    - name: Setup test database
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/benefitlens_test
      run: |
        npm run db:migrate
        npm run db:seed
        
    - name: Build application
      run: npm run build
      
    - name: Run E2E tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/benefitlens_test
        E2E_BASE_URL: http://localhost:3000
      run: npm run test:e2e
      
    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: |
          test-results/
          playwright-report/
          
    - name: Upload E2E artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-artifacts
        path: |
          test-results/e2e-artifacts/
          
  # Job 4: Security Tests
  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run npm audit
      run: npm audit --audit-level=moderate
      
    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v3
      with:
        languages: javascript
        
    - name: Autobuild
      uses: github/codeql-action/autobuild@v3
      
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  # Job 5: Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Job 6: Test Summary
  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, security-tests]
    if: always()
    
    steps:
    - name: Download all test results
      uses: actions/download-artifact@v4
      
    - name: Generate test summary
      run: |
        echo "# 🧪 BenefitLens Test Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Unit Tests
        if [ -f "unit-test-results/unit-results.json" ]; then
          echo "## Unit Tests ✅" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Passed" >> $GITHUB_STEP_SUMMARY
        else
          echo "## Unit Tests ❌" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Failed" >> $GITHUB_STEP_SUMMARY
        fi
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Integration Tests
        if [ -f "integration-test-results/integration-results.json" ]; then
          echo "## Integration Tests ✅" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Passed" >> $GITHUB_STEP_SUMMARY
        else
          echo "## Integration Tests ❌" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Failed" >> $GITHUB_STEP_SUMMARY
        fi
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # E2E Tests
        if [ -d "e2e-test-results" ]; then
          echo "## E2E Tests ✅" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Passed" >> $GITHUB_STEP_SUMMARY
        else
          echo "## E2E Tests ❌" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Failed" >> $GITHUB_STEP_SUMMARY
        fi
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Overall Status
        if [ "${{ needs.unit-tests.result }}" == "success" ] && \
           [ "${{ needs.integration-tests.result }}" == "success" ] && \
           [ "${{ needs.e2e-tests.result }}" == "success" ] && \
           [ "${{ needs.security-tests.result }}" == "success" ]; then
          echo "## 🎉 Overall Status: READY FOR PRODUCTION" >> $GITHUB_STEP_SUMMARY
        else
          echo "## ❌ Overall Status: NOT READY FOR PRODUCTION" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: Set deployment status
      if: github.ref == 'refs/heads/main'
      run: |
        if [ "${{ needs.unit-tests.result }}" == "success" ] && \
           [ "${{ needs.integration-tests.result }}" == "success" ] && \
           [ "${{ needs.e2e-tests.result }}" == "success" ] && \
           [ "${{ needs.security-tests.result }}" == "success" ]; then
          echo "DEPLOY_READY=true" >> $GITHUB_ENV
        else
          echo "DEPLOY_READY=false" >> $GITHUB_ENV
        fi
