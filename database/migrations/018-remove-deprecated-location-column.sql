-- Migration: Remove deprecated location column from companies table
-- This migration removes the legacy location column since all companies have been migrated
-- to the new multi-location system using the company_locations table
-- Date: 2025-08-12

-- First, verify that all companies have location entries in the new system
-- This query should return 0 if all companies are properly migrated
DO $$
DECLARE
    unmigrated_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO unmigrated_count
    FROM companies c
    LEFT JOIN company_locations cl ON c.id = cl.company_id
    WHERE cl.company_id IS NULL;
    
    IF unmigrated_count > 0 THEN
        RAISE EXCEPTION 'Cannot remove location column: % companies have not been migrated to the new location system', unmigrated_count;
    END IF;
    
    RAISE NOTICE 'All companies have been migrated to the new location system. Proceeding with column removal.';
END $$;

-- Remove any indexes on the location column
DROP INDEX IF EXISTS idx_companies_location;

-- Remove the deprecated location column from companies table
ALTER TABLE companies DROP COLUMN IF EXISTS location;

-- Update the table comment to reflect the change
COMMENT ON TABLE companies IS 'Companies table - uses company_locations table for multi-location support';

-- Add migration log entry
INSERT INTO migration_log (migration_name, description, applied_at) 
VALUES (
  '018-remove-deprecated-location-column', 
  'Removed deprecated location column from companies table after migration to multi-location system',
  NOW()
) ON CONFLICT (migration_name) DO NOTHING;

-- Create migration_log table if it doesn't exist (safety check)
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
