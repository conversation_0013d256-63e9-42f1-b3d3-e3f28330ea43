#!/bin/bash

# Script to restore database from backup in Docker container
set -e

# Configuration
CONTAINER_NAME="benefitlens-postgres"
DB_NAME="benefitlens"
DB_USER="benefitlens_user"
BACKUPS_DIR="./backups"

# Function to get the latest backup file
get_latest_backup() {
    ls -t "$BACKUPS_DIR"/backup_*.sql 2>/dev/null | head -n1
}

# Function to restore from a specific backup file
restore_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo "Error: Backup file '$backup_file' not found!"
        exit 1
    fi
    
    echo "Restoring database from backup: $backup_file"
    
    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    docker exec "$CONTAINER_NAME" bash -c "
        until pg_isready -U $DB_USER -d $DB_NAME; do
            echo 'Waiting for PostgreSQL...'
            sleep 2
        done
    "
    
    # Drop existing database and recreate (to ensure clean restore)
    echo "Recreating database..."
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();"
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"
    
    # Restore the backup
    echo "Restoring backup data..."
    docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" < "$backup_file"
    
    echo "Database restored successfully from $backup_file"
}

# Main script logic
if [ $# -eq 0 ]; then
    # No arguments provided, use latest backup
    LATEST_BACKUP=$(get_latest_backup)
    if [ -z "$LATEST_BACKUP" ]; then
        echo "Error: No backup files found in $BACKUPS_DIR"
        echo "Backup files should be named like: backup_YYYYMMDD_HHMMSS.sql"
        exit 1
    fi
    echo "Using latest backup: $LATEST_BACKUP"
    restore_backup "$LATEST_BACKUP"
elif [ $# -eq 1 ]; then
    # Specific backup file provided
    BACKUP_FILE="$1"
    
    # If it's just a filename, prepend the backups directory
    if [[ "$BACKUP_FILE" != */* ]]; then
        BACKUP_FILE="$BACKUPS_DIR/$BACKUP_FILE"
    fi
    
    restore_backup "$BACKUP_FILE"
else
    echo "Usage: $0 [backup_file]"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Restore from latest backup"
    echo "  $0 backup_20250822_132722.sql       # Restore from specific backup"
    echo "  $0 /path/to/backup.sql               # Restore from full path"
    echo ""
    echo "Available backups:"
    ls -la "$BACKUPS_DIR"/backup_*.sql 2>/dev/null || echo "  No backups found in $BACKUPS_DIR"
    exit 1
fi
