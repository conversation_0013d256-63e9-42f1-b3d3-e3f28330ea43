# German Multi-Location Import Guide

This guide explains how to import companies with multiple German locations using the enhanced import script.

## Overview

The import script supports importing companies with multiple German locations in both JSON and CSV formats. Each company can have multiple locations within Germany, with one designated as primary for display on company cards. The system focuses exclusively on German locations for the German market launch.

## Supported Formats

### JSON Format

```json
[
  {
    "name": "Company Name",
    "size": "medium",
    "industry": "Technology",
    "description": "Company description",
    "domain": "company.com",
    "career_url": "https://company.com/careers",
    "locations": [
      {
        "location_raw": "Frankfurt am Main, Germany",
        "is_primary": true
      },
      {
        "location_raw": "Berlin, Germany",
        "is_primary": false
      }
    ]
  }
]
```

### CSV Format

```csv
name,size,industry,description,domain,career_url,verified,locations
"Company Name",medium,Technology,"Company description",company.com,https://company.com/careers,false,"Frankfurt am Main, Germany:true|Berlin, Germany:false"
```

## Location Format

### Location Fields

- **location_raw**: The raw location string (e.g., "Frankfurt am Main, Germany")
- **is_primary**: Boolean indicating if this is the primary location for the company (shown on company cards)

### CSV Location Format

For CSV files, multiple locations are specified in a pipe-separated format:
```
"location:is_primary|location2:is_primary"
```

Example:
```
"Frankfurt am Main, Germany:true|Berlin, Germany:false|Munich, Germany:false"
```

## Location Normalization

The import script automatically normalizes German location strings using the country-state-city library:

1. **Input**: "Frankfurt am Main, Germany"
2. **Normalization**: Matches against German city database
3. **Validation**: Only German locations (country_code = 'DE') are accepted
4. **Output**: Structured location with coordinates
   - City: "Frankfurt am Main"
   - Country: "Germany"
   - Country Code: "DE"
   - Latitude: 50.1109
   - Longitude: 8.6821

**Note**: Non-German locations are automatically filtered out during import.

## Usage Examples

### Import JSON with Multiple Locations

```bash
node scripts/import-data.js --type=companies --file=data/demo-companies.json
```

### Import CSV with Multiple Locations

```bash
node scripts/import-data.js --type=companies --file=data/demo-companies.csv
```

### Dry Run (Preview Changes)

```bash
node scripts/import-data.js --type=companies --file=data/demo-companies.json --dry-run
```

## Validation Rules

### Company Validation
- Name is required
- Industry is required
- At least one valid location is required
- Domain format validation (if provided)

### Location Validation
- Each company must have at least one German location
- Each company must have exactly one primary location
- Location strings must be parseable (city, country format)
- Only German locations are accepted (non-German locations are filtered out)
- Invalid locations are skipped with warnings

## Database Schema

The import creates records in two tables:

### companies table
- Stores main company information
- `location` field contains the normalized primary location

### company_locations table
- Stores all company locations
- Links to company via `company_id`
- Includes coordinates and location metadata

## Error Handling

The import script provides comprehensive error handling:

- **Validation Errors**: Invalid data is skipped with detailed error messages
- **Database Errors**: Transaction rollback ensures data consistency
- **Location Errors**: Invalid locations are skipped, but company import continues if at least one valid location exists

## Best Practices

1. **Always run with --dry-run first** to preview changes
2. **Validate location strings** before import (use proper city, country format)
3. **Ensure one headquarters per company** in your data
4. **Use consistent location naming** (e.g., "Frankfurt am Main" not "Frankfurt")
5. **Include coordinates when possible** for better accuracy

## Troubleshooting

### Common Issues

1. **"No valid locations found"**
   - Check location string format
   - Ensure city names match country-state-city database
   - Use full city names (e.g., "Frankfurt am Main" not "Frankfurt")

2. **"Multiple headquarters found"**
   - Ensure only one location has `is_headquarters: true`
   - Check CSV format for correct boolean values

3. **"Location normalization failed"**
   - Verify city and country names
   - Check for typos in location strings
   - Use English city names when possible

### Debug Mode

Add verbose logging by setting environment variable:
```bash
DEBUG=true node scripts/import-data.js --type=companies --file=data.json
```

## Migration from Single Location

If you have existing single-location data:

1. The script automatically converts single `location` field to multi-location format
2. Single location becomes headquarters and primary location
3. No manual conversion needed

## Examples

See the following example files:
- `data/demo-companies.json` - JSON format with multiple locations
- `data/demo-companies.csv` - CSV format with multiple locations

Both files demonstrate proper formatting and can be used as templates for your own data imports.
