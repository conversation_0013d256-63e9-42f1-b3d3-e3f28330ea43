# German Companies Data Import

This document describes the German companies data import system for the BenefitLens platform.

> **Note:** The specialized German companies import script has been removed. This documentation now covers using the generic import system for German company data.

## Overview

The import system provides multiple methods to populate the database with German company data:

1. **Standalone Node.js Script** - Command-line tool for bulk imports
2. **API Endpoint** - RESTful API for programmatic imports
3. **CSV Import** - Import from CSV files with custom data

## Data Sources

### 1. Default Curated List
A pre-defined list of major German companies including:
- Volkswagen AG, BMW Group, Mercedes-Benz Group AG
- Bosch, Adidas AG, BASF SE, Bayer AG
- Deutsche Telekom AG, Allianz SE
- Zalando SE, Delivery Hero SE, N26 GmbH
- And more...

### 2. CSV File Import
Import custom company data from CSV files with the following format:

```csv
name,location,size,industry,description,domain,career_url
"Company Name","City, Germany","medium","Technology","Description","company.de","https://company.de/careers"
```

### 3. API Integration (Future)
Placeholder for future integration with:
- German Company Register (Handelsregister)
- OpenCorporates API
- Crunchbase API

## Database Schema

Companies are imported into the `companies` table with the following structure:

| Column | Type | Required | Description |
|--------|------|----------|-------------|
| `id` | UUID | Auto | Primary key |
| `name` | VARCHAR(255) | Yes | Company name |
| `location` | VARCHAR(255) | Yes | Company location |
| `size` | VARCHAR(50) | Yes | startup, small, medium, large, enterprise |
| `industry` | VARCHAR(255) | Yes | Industry category |
| `description` | TEXT | No | Company description |
| `domain` | VARCHAR(255) | No | Company domain (.de domains only) |
| `career_url` | VARCHAR(500) | No | URL to careers page |

| `created_at` | TIMESTAMP | Auto | Creation timestamp |
| `updated_at` | TIMESTAMP | Auto | Update timestamp |

## Data Validation

The import system validates:

### Required Fields
- Company name (non-empty)
- Location (must contain German location keywords)
- Size (must be valid enum value)
- Industry (non-empty)

### German-Specific Validation
- **Domain**: Must be valid .de domain format
- **Location**: Must contain German location keywords (Germany, Deutschland, Berlin, Munich, etc.)

### URL Validation
- Career URLs must be valid HTTP/HTTPS URLs

### Duplicate Prevention
- Checks for existing companies by name (case-insensitive)
- Checks for existing companies by domain (case-insensitive)

## Usage Methods

### Method 1: Node.js Script

#### Installation
```bash
# Install dependencies
npm install

# Or install csv-parser specifically
npm install csv-parser
```

#### Basic Usage
```bash
# Import companies from CSV using the generic import script
node scripts/import-data.js --type=companies --file=./data/german-companies.csv

# Dry run (preview without importing)
node scripts/import-data.js --type=companies --file=./data/german-companies.csv --dry-run
```

#### CSV Import
```bash
# Import from CSV file
node scripts/import-data.js --type=companies --file=./data/my-companies.csv

# Dry run with CSV
node scripts/import-data.js --type=companies --file=./data/my-companies.csv --dry-run
```

#### Script Options
- `--dry-run`: Preview changes without importing
- `--type`: Data type (companies or benefits)
- `--file`: Path to data file (CSV or JSON)
- `--mapping`: Custom field mapping file (optional)

**Note:** The specialized German companies import script has been removed. Use the generic `import-data.js` script for all company imports.

### Method 2: API Endpoint

#### Import Companies
```bash
POST /api/admin/import/german-companies
Content-Type: application/json
Authorization: Admin required

{
  "companies": [
    {
      "name": "Example GmbH",
      "location": "Berlin, Germany",
      "size": "medium",
      "industry": "Technology",
      "description": "Example company",
      "domain": "example.de",
      "career_url": "https://example.de/careers",

    }
  ],
  "dryRun": false
}
```

#### Get Default Companies Data
```bash
GET /api/admin/import/german-companies
Authorization: Admin required
```

### Method 3: CSV Template

Use the provided template at `data/german-companies-template.csv`:

```csv
name,location,size,industry,description,domain,career_url
"Example Company GmbH","Berlin, Germany","medium","Technology","Example technology company based in Berlin","example.de","https://example.de/careers"
"Sample AG","Munich, Germany","large","Financial Services","Sample financial services company","sample.de","https://sample.de/jobs"
```

## Environment Configuration

Set the following environment variables for database connection:

```bash
DB_USER=workwell_user
DB_HOST=localhost
DB_NAME=workwell
DB_PASSWORD=workwell_password
DB_PORT=5432
```

## Error Handling

The import system provides comprehensive error handling:

### Validation Errors
- Invalid company size values
- Missing required fields
- Invalid German domains
- Non-German locations
- Invalid URLs

### Database Errors
- Connection failures
- Constraint violations
- Duplicate key errors

### Import Statistics
Both the script and API provide detailed statistics:
- Total processed
- Successfully imported
- Skipped (duplicates)
- Errors with details

## Examples

### Example 1: Basic Import
```bash
npm run import:german-companies
```

Output:
```
[INFO] German Companies Import Script
[INFO] Source: default
[INFO] Dry run: false
[INFO] Starting import of 15 companies...
[SUCCESS] Imported: Volkswagen AG (ID: 123e4567-e89b-12d3-a456-426614174000)
[SUCCESS] Imported: BMW Group (ID: 123e4567-e89b-12d3-a456-426614174001)
...
=== Import Statistics ===
Processed: 15
Imported: 15
Skipped: 0
Errors: 0
========================
```

### Example 2: CSV Import with Validation Error
```bash
node scripts/import-german-companies.js --source=csv --file=./data/companies.csv
```

Output:
```
[ERROR] Validation failed for Invalid Company: Domain must be a valid German domain (.de)
[WARN] Skipping duplicate company: Existing Company (existing: Existing Company)
[SUCCESS] Imported: New Company (ID: 123e4567-e89b-12d3-a456-426614174002)
```

### Example 3: API Import Response
```json
{
  "success": true,
  "imported": 2,
  "skipped": 1,
  "errors": 0,
  "details": [
    {
      "company": "New Company GmbH",
      "status": "imported",
      "message": "Successfully imported (ID: 123e4567-e89b-12d3-a456-426614174003)"
    },
    {
      "company": "Another Company AG",
      "status": "imported", 
      "message": "Successfully imported (ID: 123e4567-e89b-12d3-a456-426614174004)"
    },
    {
      "company": "Existing Company",
      "status": "skipped",
      "message": "Duplicate company (existing: Existing Company)"
    }
  ]
}
```

## Testing

### Test the Import Script
```bash
# Test with dry run
npm run import:german-companies:dry-run

# Test CSV parsing
node scripts/import-german-companies.js --source=csv --file=./data/german-companies-template.csv --dry-run
```

### Test the API Endpoint
```bash
# Test with curl (requires admin authentication)
curl -X POST "http://localhost:3000/api/admin/import/german-companies" \
  -H "Content-Type: application/json" \
  -H "Cookie: session_token=YOUR_ADMIN_SESSION_TOKEN" \
  -d '{
    "companies": [
      {
        "name": "Test Company GmbH",
        "location": "Berlin, Germany",
        "size": "startup",
        "industry": "Technology",
        "domain": "test.de"
      }
    ],
    "dryRun": true
  }'
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check environment variables
   - Ensure PostgreSQL is running
   - Verify database credentials

2. **CSV Parsing Error**
   - Check CSV format and encoding
   - Ensure all required columns are present
   - Verify file path is correct

3. **Validation Errors**
   - Check company size values (must be: startup, small, medium, large, enterprise)
   - Ensure domains end with .de
   - Verify locations contain German keywords

4. **Permission Errors**
   - API endpoints require admin authentication
   - Check user role and session token

### Debug Mode
Add console.log statements to the script for debugging:

```javascript
// Add to scripts/import-german-companies.js
console.log('Processing company:', company);
console.log('Validation errors:', validationErrors);
```

## Future Enhancements

1. **API Integration**: Connect to external company databases
2. **Batch Processing**: Handle large datasets efficiently
3. **Data Enrichment**: Automatically fetch additional company information
4. **Scheduling**: Automated periodic imports
5. **Web Interface**: Admin UI for managing imports
6. **Data Quality**: Enhanced validation and data cleaning
