#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create an admin user account
 * Usage: node scripts/create-admin.js
 */

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'workwell',
  user: 'workwell_user',
  password: 'workwell_password',
});

async function createAdminUser() {
  const client = await pool.connect();
  
  try {
    // Admin user details
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123'; // Change this to a secure password
    const firstName = 'Admin';
    const lastName = 'User';
    const role = 'admin';
    
    console.log('🔍 Checking if admin user already exists...');
    
    // Check if admin user already exists
    const existingUser = await client.query(
      'SELECT id, email, role FROM users WHERE email = $1',
      [adminEmail.toLowerCase()]
    );
    
    if (existingUser.rows.length > 0) {
      const user = existingUser.rows[0];
      if (user.role === 'admin') {
        console.log('✅ Admin user already exists:', user.email);
        console.log('📧 Email:', user.email);
        console.log('🔑 Password: admin123');
        return;
      } else {
        // Update existing user to admin
        console.log('🔄 Updating existing user to admin role...');
        await client.query(
          'UPDATE users SET role = $1 WHERE email = $2',
          ['admin', adminEmail.toLowerCase()]
        );
        console.log('✅ User updated to admin role:', user.email);
        console.log('📧 Email:', user.email);
        console.log('🔑 Use your existing password');
        return;
      }
    }
    
    console.log('👤 Creating new admin user...');
    
    // Hash password
    const passwordHash = await bcrypt.hash(adminPassword, 10);
    
    // Create admin user
    const result = await client.query(
      `INSERT INTO users (email, password_hash, first_name, last_name, role, email_verified)
       VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
      [adminEmail.toLowerCase(), passwordHash, firstName, lastName, role, true]
    );
    
    const newUser = result.rows[0];
    
    console.log('🎉 Admin user created successfully!');
    console.log('📧 Email:', newUser.email);
    console.log('🔑 Password:', adminPassword);
    console.log('👤 Name:', `${newUser.first_name} ${newUser.last_name}`);
    console.log('🛡️ Role:', newUser.role);
    console.log('');
    console.log('🚀 You can now sign in at http://localhost:3000/sign-in');
    console.log('🔧 Access admin dashboard at http://localhost:3000/admin');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
createAdminUser().catch(console.error);
