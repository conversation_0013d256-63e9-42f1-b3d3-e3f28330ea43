version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: benefitlens-postgres
    environment:
      POSTGRES_DB: benefitlens
      POSTGRES_USER: benefitlens_user
      POSTGRES_PASSWORD: benefitlens_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U benefitlens_user -d benefitlens"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - benefitlens-network

  # BenefitLens Application (Containerized Development)
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        NEXT_PUBLIC_APP_URL: http://localhost:3000
        NODE_ENV: development
    image: benefitlens:dev-local
    container_name: benefitlens-app
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=****************************************************************/benefitlens
      - CACHE_TYPE=postgresql
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - SESSION_SECRET=dev-session-secret-change-in-development
      - NODE_ENV=development
      - USE_LOCAL_AUTH=true
      - FROM_EMAIL=noreply@localhost
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
      - SMTP_USER=
      - SMTP_PASS=
      - LOG_LEVEL=${LOG_LEVEL}
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - benefitlens-network

  # Adminer for database management (optional)
  adminer:
    image: adminer:latest
    container_name: benefitlens-adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    networks:
      - benefitlens-network

  # MailHog for email testing (optional)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: benefitlens-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - benefitlens-network

volumes:
  postgres_data:

networks:
  benefitlens-network:
    driver: bridge
