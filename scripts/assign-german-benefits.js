#!/usr/bin/env node

/**
 * German Company Benefits Assignment Script
 * 
 * This script assigns typical German benefits to German companies in the database.
 * It creates realistic benefit assignments based on company size and industry.
 * 
 * Usage:
 *   node scripts/assign-german-benefits.js [--dry-run] [--company=COMPANY_NAME]
 * 
 * Options:
 *   --dry-run: Preview changes without actually assigning benefits
 *   --company: Assign benefits to specific company only
 */

const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'workwell_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'workwell',
  password: process.env.DB_PASSWORD || 'workwell_password',
  port: process.env.DB_PORT || 5432,
});

// German benefit assignment patterns by company size and industry
const BENEFIT_PATTERNS = {
  // Enterprise companies (DAX companies)
  enterprise: {
    mandatory: [
      'Statutory Health Insurance (Gesetzliche Krankenversicherung)',
      'Statutory Vacation (Gesetzlicher Urlaub)',
      'Sick Leave (Lohnfortzahlung im Krankheitsfall)',
      'Parental Leave (Elternzeit)',
      'Company Pension Scheme (Betriebliche Altersvorsorge)',
      'Christmas Bonus (Weihnachtsgeld)',
      'Flexible Working Hours (Gleitzeit)',
      'Remote Work (Homeoffice)'
    ],
    common: [
      'Private Health Insurance (Private Krankenversicherung)',
      'Holiday Bonus (Urlaubsgeld)',
      'Extended Vacation Days (Zusätzliche Urlaubstage)',
      'Capital-Forming Benefits (Vermögenswirksame Leistungen)',
      'Profit Sharing (Gewinnbeteiligung)',
      'Employee Stock Purchase Plan (Mitarbeiterbeteiligung)',
      'Training Budget (Weiterbildungsbudget)',
      'Language Learning Support (Sprachkurse)',
      'Conference Attendance (Konferenz-Teilnahme)',
      'Job Ticket (Jobticket)',
      'Company Car (Dienstwagen)',
      'Meal Vouchers (Essensgutscheine)',
      'Company Cafeteria (Betriebskantine)',
      'Childcare Support (Kinderbetreuung)',
      'Mobile Phone Allowance (Handy-Zuschuss)',
      'Home Office Equipment (Homeoffice-Ausstattung)'
    ],
    wellness: [
      'Urban Sports Club Membership',
      'EGYM Wellpass',
      'Hansefit Membership',
      'Company Sports Teams (Betriebssport)',
      'Massage Services (Massagen am Arbeitsplatz)',
      'Ergonomic Workplace Setup (Ergonomischer Arbeitsplatz)'
    ]
  },

  // Large companies (MDAX companies)
  large: {
    mandatory: [
      'Statutory Health Insurance (Gesetzliche Krankenversicherung)',
      'Statutory Vacation (Gesetzlicher Urlaub)',
      'Sick Leave (Lohnfortzahlung im Krankheitsfall)',
      'Parental Leave (Elternzeit)',
      'Company Pension Scheme (Betriebliche Altersvorsorge)',
      'Flexible Working Hours (Gleitzeit)',
      'Remote Work (Homeoffice)'
    ],
    common: [
      'Christmas Bonus (Weihnachtsgeld)',
      'Extended Vacation Days (Zusätzliche Urlaubstage)',
      'Training Budget (Weiterbildungsbudget)',
      'Job Ticket (Jobticket)',
      'Meal Vouchers (Essensgutscheine)',
      'Free Coffee & Snacks (Kostenlose Verpflegung)',
      'Bike Leasing (Dienstfahrrad)',
      'Mobile Phone Allowance (Handy-Zuschuss)'
    ],
    wellness: [
      'Urban Sports Club Membership',
      'EGYM Wellpass',
      'Company Sports Teams (Betriebssport)',
      'Ergonomic Workplace Setup (Ergonomischer Arbeitsplatz)'
    ]
  },

  // Medium companies
  medium: {
    mandatory: [
      'Statutory Health Insurance (Gesetzliche Krankenversicherung)',
      'Statutory Vacation (Gesetzlicher Urlaub)',
      'Sick Leave (Lohnfortzahlung im Krankheitsfall)',
      'Parental Leave (Elternzeit)',
      'Flexible Working Hours (Gleitzeit)',
      'Remote Work (Homeoffice)'
    ],
    common: [
      'Christmas Bonus (Weihnachtsgeld)',
      'Training Budget (Weiterbildungsbudget)',
      'Job Ticket (Jobticket)',
      'Free Coffee & Snacks (Kostenlose Verpflegung)',
      'Bike Leasing (Dienstfahrrad)'
    ],
    wellness: [
      'Urban Sports Club Membership',
      'Company Sports Teams (Betriebssport)'
    ]
  },

  // Small companies and startups
  small: {
    mandatory: [
      'Statutory Health Insurance (Gesetzliche Krankenversicherung)',
      'Statutory Vacation (Gesetzlicher Urlaub)',
      'Sick Leave (Lohnfortzahlung im Krankheitsfall)',
      'Parental Leave (Elternzeit)',
      'Flexible Working Hours (Gleitzeit)',
      'Remote Work (Homeoffice)'
    ],
    common: [
      'Free Coffee & Snacks (Kostenlose Verpflegung)',
      'Bike Leasing (Dienstfahrrad)',
      'Training Budget (Weiterbildungsbudget)'
    ],
    wellness: [
      'Urban Sports Club Membership'
    ]
  }
};

// Industry-specific additional benefits
const INDUSTRY_BENEFITS = {
  'Technology': [
    'Language Learning Support (Sprachkurse)',
    'Conference Attendance (Konferenz-Teilnahme)',
    'Home Office Equipment (Homeoffice-Ausstattung)',
    'Internet Allowance (Internet-Zuschuss)',
    'Study Leave (Bildungsurlaub)',
    'Viertage Woche (4-Day Work Week)'
  ],
  'Automotive': [
    'Company Car (Dienstwagen)',
    'Parking Allowance (Parkplatz-Zuschuss)',
    'Travel Allowance (Reisekostenzuschuss)'
  ],
  'Financial Services': [
    'Employee Stock Purchase Plan (Mitarbeiterbeteiligung)',
    'Profit Sharing (Gewinnbeteiligung)',
    'Capital-Forming Benefits (Vermögenswirksame Leistungen)',
    'Private Health Insurance (Private Krankenversicherung)'
  ],
  'Healthcare': [
    'Company Health Check-ups (Betriebsärztliche Untersuchungen)',
    'Occupational Health Services (Arbeitsmedizin)',
    'Mental Health Support (Psychologische Betreuung)',
    'Private Health Insurance (Private Krankenversicherung)'
  ],
  'Pharmaceuticals': [
    'Company Health Check-ups (Betriebsärztliche Untersuchungen)',
    'Private Health Insurance (Private Krankenversicherung)',
    'Study Leave (Bildungsurlaub)'
  ],
  'E-commerce': [
    'Flexible Working Hours (Gleitzeit)',
    'Remote Work (Homeoffice)',
    'Home Office Equipment (Homeoffice-Ausstattung)',
    'Internet Allowance (Internet-Zuschuss)',
    'Viertage Woche (4-Day Work Week)'
  ]
};

// Logging utility
class Logger {
  constructor(isDryRun = false) {
    this.isDryRun = isDryRun;
    this.stats = {
      companies: 0,
      assignments: 0,
      skipped: 0,
      errors: 0
    };
  }

  info(message) {
    console.log(`[INFO] ${message}`);
  }

  warn(message) {
    console.log(`[WARN] ${message}`);
  }

  error(message) {
    console.log(`[ERROR] ${message}`);
  }

  success(message) {
    console.log(`[SUCCESS] ${message}`);
  }

  dryRun(message) {
    if (this.isDryRun) {
      console.log(`[DRY-RUN] ${message}`);
    }
  }

  printStats() {
    console.log('\n=== Assignment Statistics ===');
    console.log(`Companies processed: ${this.stats.companies}`);
    console.log(`Benefits assigned: ${this.stats.assignments}`);
    console.log(`Skipped (duplicates): ${this.stats.skipped}`);
    console.log(`Errors: ${this.stats.errors}`);
    console.log('=============================\n');
  }
}

// Database operations
async function getGermanCompanies(companyFilter = null) {
  const client = await pool.connect();
  try {
    let query = `
      SELECT id, name, size, industry, location
      FROM companies 
      WHERE location LIKE '%Germany%' OR location LIKE '%Deutschland%'
    `;
    let params = [];

    if (companyFilter) {
      query += ` AND LOWER(name) LIKE LOWER($1)`;
      params = [`%${companyFilter}%`];
    }

    query += ` ORDER BY size DESC, name`;

    const result = await client.query(query, params);
    return result.rows;
  } finally {
    client.release();
  }
}

async function getBenefitsByNames(benefitNames) {
  const client = await pool.connect();
  try {
    const query = `
      SELECT id, name
      FROM benefits 
      WHERE name = ANY($1)
    `;
    const result = await client.query(query, [benefitNames]);
    return result.rows;
  } finally {
    client.release();
  }
}

async function checkExistingBenefit(companyId, benefitId) {
  const client = await pool.connect();
  try {
    const query = `
      SELECT id
      FROM company_benefits 
      WHERE company_id = $1 AND benefit_id = $2
    `;
    const result = await client.query(query, [companyId, benefitId]);
    return result.rows.length > 0;
  } finally {
    client.release();
  }
}

async function assignBenefit(companyId, benefitId, isVerified, logger) {
  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)
      VALUES ($1, $2, $3, 'system')
      RETURNING id
    `;

    if (logger.isDryRun) {
      logger.dryRun(`Would assign benefit to company`);
      return { id: 'dry-run-id' };
    }

    const result = await client.query(query, [companyId, benefitId, isVerified]);
    return result.rows[0];
  } finally {
    client.release();
  }
}

// Main assignment function
async function assignBenefitsToCompany(company, logger) {
  const companySize = company.size || 'medium';
  const companyIndustry = company.industry || '';

  logger.info(`Processing ${company.name} (${companySize}, ${companyIndustry})`);

  // Get benefit pattern for company size
  const pattern = BENEFIT_PATTERNS[companySize] || BENEFIT_PATTERNS.medium;

  // Collect all benefits to assign
  const benefitsToAssign = [
    ...pattern.mandatory,
    ...pattern.common.slice(0, Math.floor(pattern.common.length * 0.7)), // 70% of common benefits
    ...pattern.wellness.slice(0, Math.floor(pattern.wellness.length * 0.5)) // 50% of wellness benefits
  ];

  // Add industry-specific benefits
  if (INDUSTRY_BENEFITS[companyIndustry]) {
    const industryBenefits = INDUSTRY_BENEFITS[companyIndustry];
    benefitsToAssign.push(...industryBenefits.slice(0, Math.floor(industryBenefits.length * 0.6)));
  }

  // Remove duplicates
  const uniqueBenefits = [...new Set(benefitsToAssign)];

  // Get benefit IDs from database
  const benefits = await getBenefitsByNames(uniqueBenefits);
  const benefitMap = new Map(benefits.map(b => [b.name, b.id]));

  let assigned = 0;
  let skipped = 0;

  for (const benefitName of uniqueBenefits) {
    const benefitId = benefitMap.get(benefitName);

    if (!benefitId) {
      logger.warn(`Benefit not found: ${benefitName}`);
      continue;
    }

    // Check if already assigned
    const exists = await checkExistingBenefit(company.id, benefitId);
    if (exists) {
      skipped++;
      continue;
    }

    // Determine if benefit should be verified
    const isVerified = pattern.mandatory.includes(benefitName);

    try {
      await assignBenefit(company.id, benefitId, isVerified, logger);
      assigned++;

      if (!logger.isDryRun) {
        logger.success(`Assigned: ${benefitName} (${isVerified ? 'verified' : 'unverified'})`);
      }
    } catch (error) {
      logger.error(`Failed to assign ${benefitName}: ${error.message}`);
      logger.stats.errors++;
    }
  }

  logger.info(`Company ${company.name}: ${assigned} benefits assigned, ${skipped} skipped`);
  logger.stats.assignments += assigned;
  logger.stats.skipped += skipped;
}

// Main execution function
async function assignBenefitsToCompanies(companies, logger) {
  logger.info(`Starting benefit assignment for ${companies.length} German companies...`);

  for (const company of companies) {
    logger.stats.companies++;

    try {
      await assignBenefitsToCompany(company, logger);
    } catch (error) {
      logger.error(`Failed to process company ${company.name}: ${error.message}`);
      logger.stats.errors++;
    }
  }

  logger.printStats();
}

// CLI argument parsing
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    company: null
  };

  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--company=')) {
      options.company = arg.split('=')[1];
    } else if (arg === '--help' || arg === '-h') {
      printHelp();
      process.exit(0);
    }
  }

  return options;
}

function printHelp() {
  console.log(`
German Company Benefits Assignment Script

Usage:
  node scripts/assign-german-benefits.js [options]

Options:
  --dry-run              Preview changes without actually assigning benefits
  --company=NAME         Assign benefits to specific company only
  --help, -h            Show this help message

Examples:
  node scripts/assign-german-benefits.js
  node scripts/assign-german-benefits.js --dry-run
  node scripts/assign-german-benefits.js --company="SAP SE"
  node scripts/assign-german-benefits.js --company="BMW" --dry-run

Benefit Assignment Logic:
  - Enterprise companies: Full benefit packages (mandatory + 70% common + 50% wellness)
  - Large companies: Comprehensive packages (mandatory + 60% common + 40% wellness)
  - Medium companies: Standard packages (mandatory + 50% common + 30% wellness)
  - Small companies: Basic packages (mandatory + 30% common + 20% wellness)

  Plus industry-specific benefits based on company sector.
`);
}

// Main execution
async function main() {
  try {
    const options = parseArguments();
    const logger = new Logger(options.dryRun);

    logger.info('German Company Benefits Assignment Script');
    logger.info(`Company filter: ${options.company || 'all German companies'}`);
    logger.info(`Dry run: ${options.dryRun}`);

    if (options.dryRun) {
      logger.info('DRY RUN MODE - No changes will be made to the database');
    }

    // Get German companies
    const companies = await getGermanCompanies(options.company);

    if (companies.length === 0) {
      logger.warn('No German companies found matching criteria');
      process.exit(0);
    }

    logger.info(`Found ${companies.length} German companies to process`);

    // Assign benefits
    await assignBenefitsToCompanies(companies, logger);

    logger.info('Benefit assignment completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error(`[FATAL] ${error.message}`);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  assignBenefitsToCompanies,
  BENEFIT_PATTERNS,
  INDUSTRY_BENEFITS
};
