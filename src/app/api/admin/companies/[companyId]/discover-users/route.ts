import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { discoverAndNotifyUsers } from '@/lib/user-discovery'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin()
    
    const { companyId } = await params

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    const discoveryResult = await discoverAndNotifyUsers(companyId)

    return NextResponse.json({
      success: true,
      message: `Discovery complete for ${discoveryResult.companyName}`,
      result: discoveryResult
    })

  } catch (error) {
    console.error('Error discovering users:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to discover users' },
      { status: 500 }
    )
  }
}
