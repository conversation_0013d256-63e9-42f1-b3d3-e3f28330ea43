#!/usr/bin/env node

// Complete migration script from Redis to PostgreSQL

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

console.log('🚀 Starting migration from Redis to PostgreSQL...\n')

// Step 1: Run database migrations
console.log('📊 Running database migrations...')
try {
  // Apply PostgreSQL session optimizations
  execSync('psql $DATABASE_URL -f database/migrations/001_optimize_postgresql_sessions.sql', { stdio: 'inherit' })
  console.log('✅ Session optimization migration applied')
  
  // Apply PostgreSQL caching system
  execSync('psql $DATABASE_URL -f database/migrations/002_postgresql_caching_system.sql', { stdio: 'inherit' })
  console.log('✅ Caching system migration applied')
} catch (error) {
  console.error('❌ Database migration failed:', error.message)
  console.log('💡 Make sure DATABASE_URL is set and PostgreSQL is running')
  process.exit(1)
}

// Step 2: Update package.json
console.log('\n📦 Updating package.json...')
try {
  execSync('node scripts/update-package-json.js', { stdio: 'inherit' })
} catch (error) {
  console.error('❌ Failed to update package.json:', error.message)
}

// Step 3: Update environment files
console.log('\n🔧 Updating environment configuration...')
try {
  execSync('node scripts/update-env-example.js', { stdio: 'inherit' })
} catch (error) {
  console.error('❌ Failed to update .env.example:', error.message)
}

// Step 4: Remove Redis dependencies
console.log('\n🗑️  Removing Redis dependencies...')
try {
  execSync('npm uninstall redis @types/redis', { stdio: 'inherit' })
  console.log('✅ Redis packages removed')
} catch (error) {
  console.log('⚠️  Redis packages may not have been installed')
}

// Step 5: Create cron job for cache maintenance
console.log('\n⏰ Setting up cache maintenance...')
const cronJobContent = `#!/bin/bash
# PostgreSQL Cache Maintenance Cron Job
# Add this to your crontab: 0 * * * * /path/to/this/script

cd "$(dirname "$0")/.."
export NODE_ENV=production
export DATABASE_URL="your_database_url_here"

# Run cache cleanup every hour
node scripts/session-cleanup.js

# Refresh materialized views every 6 hours (adjust as needed)
if [ $(($(date +%H) % 6)) -eq 0 ]; then
  node -e "require('./src/lib/postgresql-cache').refreshCacheViews().then(() => console.log('Cache views refreshed'))"
fi
`

fs.writeFileSync('scripts/cache-maintenance.sh', cronJobContent)
execSync('chmod +x scripts/cache-maintenance.sh')
console.log('✅ Cache maintenance script created')

// Step 6: Update README
console.log('\n📝 Updating documentation...')
const readmePath = 'README.md'
if (fs.existsSync(readmePath)) {
  let readme = fs.readFileSync(readmePath, 'utf8')
  
  // Update tech stack section
  readme = readme.replace(/- \*\*Database\*\*: PostgreSQL[\s\S]*?Redis.*$/gm, '- **Database**: PostgreSQL with built-in caching')
  readme = readme.replace(/- Redis \(for session storage and caching\)/g, '')
  readme = readme.replace(/- Redis connection/g, '')
  
  // Update prerequisites
  readme = readme.replace(/- Redis.*$/gm, '')
  
  // Update installation steps
  readme = readme.replace(/This will start PostgreSQL, Redis, and MailHog containers/g, 'This will start PostgreSQL and MailHog containers')
  readme = readme.replace(/# Start database and Redis/g, '# Start database')
  
  fs.writeFileSync(readmePath, readme)
  console.log('✅ README.md updated')
}

// Step 7: Provide next steps
console.log('\n🎉 Migration completed successfully!\n')
console.log('📋 Next steps:')
console.log('1. Update your production environment variables to remove REDIS_URL')
console.log('2. Add the cache maintenance cron job:')
console.log('   crontab -e')
console.log('   0 * * * * /path/to/your/project/scripts/cache-maintenance.sh')
console.log('3. Restart your application')
console.log('4. Monitor cache performance with: npm run cache:stats')
console.log('5. Test session management and rate limiting functionality')
console.log('\n💡 Benefits of this migration:')
console.log('- Simplified architecture (one less service to manage)')
console.log('- Reduced infrastructure costs')
console.log('- Better data consistency')
console.log('- Easier backup and recovery')
console.log('- PostgreSQL\'s powerful caching capabilities')

console.log('\n🔍 To verify the migration:')
console.log('- Check session management: npm run dev and test login/logout')
console.log('- Check caching: npm run cache:stats')
console.log('- Check rate limiting: Make rapid API requests')
console.log('- Monitor performance: Check application logs')

console.log('\n✨ Your application is now Redis-free and running on PostgreSQL only!')
