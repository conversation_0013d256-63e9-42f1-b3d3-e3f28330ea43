import { NextRequest, NextResponse } from 'next/server'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { getAnalyticsOverview } from '@/lib/analytics-tracker'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d'

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    if (accessInfo.level === 'none') {
      return NextResponse.json(
        { error: 'Authentication required to access analytics' },
        { status: 401 }
      )
    }

    // If user is on demo mode, return demo data
    if (accessInfo.isDemoMode) {
      return NextResponse.json({
        period,
        overview: {
          total_searches: 12450,
          company_views: 89320,
          active_companies: 156,
          avg_engagement: 78.5
        },
        is_demo_data: true,
        demo_notice: 'This is preview data. Upgrade to Premium to see real analytics.',
        generated_at: new Date().toISOString()
      })
    }

    // Get real analytics data for paying customers
    const analyticsData = await getAnalyticsOverview(period)
    return NextResponse.json(analyticsData)

  } catch (error) {
    console.error('Error fetching overview analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch overview analytics' },
      { status: 500 }
    )
  }
}
