-- Migration: Add 'cancelled' status to benefit removal disputes
-- Date: 2025-08-01

-- Drop the existing constraint
ALTER TABLE benefit_removal_disputes 
DROP CONSTRAINT benefit_removal_disputes_status_check;

-- Add the new constraint with 'cancelled' status
ALTER TABLE benefit_removal_disputes 
ADD CONSTRAINT benefit_removal_disputes_status_check 
CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled'));

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('012-add-cancelled-status-to-disputes', 'Add cancelled status to benefit removal disputes table');
