# BenefitLens Application: User & Admin Flows Overview

## 1. Authentication Flows
### 1.1. Sign Up
- **UI:** `/sign-up` → `SignUpForm`
- **API:** `POST /api/auth/sign-up`
  - User submits email, first name, last name.
  - Receives a magic link via email.
  - Clicks magic link to complete registration.

### 1.2. Sign In
- **UI:** `/sign-in` → `SignInForm`
- **API:** `POST /api/auth/sign-in`
  - User submits email.
  - Receives a magic link via email.
  - Clicks magic link to sign in.

### 1.3. Magic Link Verification
- **UI:** `/auth/magic-link` (and related component)
- **API:** `POST /api/auth/magic-link`
  - Verifies the token from the magic link.
  - Creates a session and redirects to dashboard.

### 1.4. Sign Out
- **UI:** User menu (UserButton)
- **API:** `POST /api/auth/sign-out`
  - Ends user session.

### 1.5. Profile Update
- **UI:** User menu (UserButton → settings)
- **API:** `PATCH /api/auth/update-profile`
  - User can update first name, last name, email.

---

## 2. Company Flows
### 2.1. Company Search & Listing
- **UI:** `/` (Home), `/companies/[id]`
  - Users can search and view companies.

### 2.2. Company Profile & Benefits
- **UI:** `/companies/[id]`
  - Shows company details, benefits, and verification status.
  - Users can view, verify, and save companies.

### 2.3. Company Verification
- **UI:** `/verify-company`, `/auth/verify-company`
- **API:** `POST /api/auth/verify-company`
  - Users can verify their association with a company (e.g., via email domain).

### 2.4. Save/Unsave Company
- **UI:** `/saved-companies`
- **API:** `POST/DELETE /api/saved-companies/[companyId]`
  - Users can save or unsave companies for quick access.

---

## 3. Benefits Flows
### 3.1. Browse All Benefits
- **UI:** `/benefits`
  - Users can browse a list of all available benefits.

### 3.2. Benefit Verification
- **UI:** `/companies/[id]` (BenefitVerification component)
- **API:** 
  - `POST /api/benefit-verifications`
  - `GET /api/benefit-verifications/[companyBenefitId]/authorization`
  - Users can verify specific benefits for a company.

---

## 4. Analytics Flows
### 4.1. Analytics Dashboard
- **UI:** `/analytics`
  - Users (possibly admins) can view analytics dashboards.

### 4.2. Company Analytics
- **API:** `GET /api/analytics/company/[id]`
  - Company-specific analytics data.

### 4.3. Export Analytics
- **API:** `GET /api/analytics/export`
  - Export analytics data.

### 4.4. Top Companies & Search Trends
- **API:** 
  - `GET /api/analytics/top-companies`
  - `GET /api/analytics/search-trends`

---

## 5. Admin Flows
### 5.1. Admin Dashboard
- **UI:** `/admin`
  - Platform administration for companies, users, benefits, disputes.

### 5.2. Manage Companies
- **API:** 
  - `GET/POST/PATCH/DELETE /api/admin/companies`
  - `GET/POST/PATCH/DELETE /api/admin/companies/[companyId]`
  - Manage company details, benefits, and user discovery.

### 5.3. Manage Benefits
- **API:** 
  - `GET/POST/PATCH/DELETE /api/admin/benefits`
  - `GET/POST/PATCH/DELETE /api/admin/companies/[companyId]/benefits`
  - Verify, import, export, and bulk update company benefits.

### 5.4. Manage Users
- **API:** 
  - `GET/POST/PATCH/DELETE /api/admin/users`
  - `PATCH /api/admin/users/[userId]/payment-status`
  - Resend verification, send password reset.

### 5.5. Disputed Benefits
- **API:** `GET/POST/PATCH/DELETE /api/admin/disputed-benefits`
  - Manage and resolve benefit disputes.

### 5.6. Import/Export Data
- **API:** 
  - `POST /api/admin/import/german-companies`
  - `GET /api/admin/analytics/export`
  - Import companies, export analytics/users.

### 5.7. Missing Company Reports
- **API:** 
  - `POST /api/report-missing-company`
  - `GET/POST/PATCH/DELETE /api/admin/missing-company-reports`
  - Users can report missing companies; admins manage these reports.

---

## 6. Other Flows
### 6.1. Pricing
- **UI:** `/pricing`
  - View pricing plans.

### 6.2. About
- **UI:** `/about`
  - Information about the platform.

---

## 7. Verification & Authorization Flows
- **Company/Benefit Authorization:** 
  - `GET /api/companies/[id]/authorization`
  - `GET /api/benefit-verifications/[companyBenefitId]/authorization`
- **Company Verification Notice:** 
  - UI component on company pages for users not yet authorized.

---

## 8. Debug/Test Flows
- **API:** `/api/debug/users`
- **UI:** `/test-benefits`
  - For internal testing and QA.

---

# Checklist for Verification

For each flow above, verify:
- UI is accessible and renders as expected.
- All form submissions and actions trigger the correct API calls.
- API endpoints respond with correct status and data.
- Success and error states are handled and displayed.
- Permissions and authorization are enforced (e.g., admin-only routes, company association).
- Email flows (magic link, verification, password reset) are delivered and function.
- Data changes are reflected in the UI and database as expected.
