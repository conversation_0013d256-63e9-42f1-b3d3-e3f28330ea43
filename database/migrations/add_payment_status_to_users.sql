-- Migration: Add payment status to users table (PostgreSQL version)
-- This migration adds payment_status field to the users table

-- Add payment_status column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'payment_status') THEN
        ALTER TABLE users ADD COLUMN payment_status VARCHAR(50) CHECK (payment_status IN ('free', 'paying', 'trial')) DEFAULT 'free';
    END IF;
END $$;

-- Add trial_expires_at column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'trial_expires_at') THEN
        ALTER TABLE users ADD COLUMN trial_expires_at TIMESTAMP WITH TIME ZONE NULL;
    END IF;
END $$;

-- Update existing users to have default values
UPDATE users SET payment_status = 'free' WHERE payment_status IS NULL;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_payment_status ON users(payment_status);
CREATE INDEX IF NOT EXISTS idx_users_trial_expires_at ON users(trial_expires_at);
