/**
 * Admin Analytics Management Flow Tests
 * Tests for admin analytics operations, data reset, and system monitoring
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('Admin Analytics Management Flows', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(mockFetch).mockClear()
    mockQuery.mockReset()
  })

  describe('Analytics Data Reset', () => {
    it('should reset all analytics data', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'All analytics data reset successfully',
          resetActions: [
            'Cleared company page views',
            'Cleared search queries',
            'Cleared benefit interactions',
            'Cleared daily summaries',
            'Reset analytics counters'
          ],
          recordsAffected: 15420
        })
      })

      const response = await fetch('/api/admin/analytics/reset?type=all', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('All analytics data reset successfully')
      expect(result.resetActions).toHaveLength(5)
      expect(result.recordsAffected).toBe(15420)
    })

    it('should reset specific analytics data type', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Company page views reset successfully',
          resetActions: [
            'Cleared company page views table',
            'Reset company view counters'
          ],
          recordsAffected: 5230
        })
      })

      const response = await fetch('/api/admin/analytics/reset?type=company_views', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toContain('Company page views reset')
      expect(result.recordsAffected).toBe(5230)
    })

    it('should reset analytics data by date range', async () => {
      const resetData = {
        type: 'date_range',
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Analytics data reset for January 2024',
          resetActions: [
            'Cleared data from 2024-01-01 to 2024-01-31',
            'Updated aggregated summaries'
          ],
          recordsAffected: 2150
        })
      })

      const response = await fetch('/api/admin/analytics/reset', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(resetData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toContain('January 2024')
      expect(result.recordsAffected).toBe(2150)
    })

    it('should require confirmation for reset operations', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ 
          error: 'Confirmation required for analytics reset',
          confirmationToken: 'reset-token-123'
        })
      })

      const response = await fetch('/api/admin/analytics/reset?type=all', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Confirmation required for analytics reset')
      expect(result.confirmationToken).toBe('reset-token-123')
    })

    it('should handle confirmed reset operation', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Analytics data reset confirmed and executed'
        })
      })

      const response = await fetch('/api/admin/analytics/reset?type=all', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ 
          confirmationToken: 'reset-token-123',
          confirmed: true 
        })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toContain('confirmed and executed')
    })
  })

  describe('System Analytics Dashboard', () => {
    it('should get comprehensive system analytics', async () => {
      const mockSystemAnalytics = {
        overview: {
          totalEvents: 125000,
          dailyEvents: 2500,
          weeklyEvents: 17500,
          monthlyEvents: 75000
        },
        eventBreakdown: {
          companyViews: 45000,
          searchQueries: 35000,
          benefitInteractions: 25000,
          userRegistrations: 8000,
          other: 12000
        },
        performance: {
          averageResponseTime: 150,
          errorRate: 0.02,
          cacheHitRate: 0.85,
          databaseConnections: 12
        },
        userEngagement: {
          dailyActiveUsers: 320,
          weeklyActiveUsers: 850,
          monthlyActiveUsers: 1100,
          averageSessionDuration: 420
        },
        contentStats: {
          totalCompanies: 150,
          totalBenefits: 75,
          totalUsers: 1250,
          verifiedCompanies: 120
        },
        trends: {
          userGrowth: 15.5,
          companyGrowth: 8.2,
          engagementGrowth: 12.3
        }
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockSystemAnalytics
      })

      const response = await fetch('/api/admin/analytics/dashboard', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.overview.totalEvents).toBe(125000)
      expect(result.eventBreakdown.companyViews).toBe(45000)
      expect(result.performance.errorRate).toBe(0.02)
      expect(result.userEngagement.dailyActiveUsers).toBe(320)
      expect(result.trends.userGrowth).toBe(15.5)
    })

    it('should get analytics data with date filtering', async () => {
      const mockFilteredAnalytics = {
        dateRange: {
          start: '2024-01-01',
          end: '2024-01-31'
        },
        events: 25000,
        topCompanies: [
          { name: 'Tech Corp', views: 1200 },
          { name: 'Design Studio', views: 980 }
        ],
        topBenefits: [
          { name: 'Health Insurance', interactions: 850 },
          { name: 'Remote Work', interactions: 720 }
        ]
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockFilteredAnalytics
      })

      const response = await fetch('/api/admin/analytics/dashboard?start=2024-01-01&end=2024-01-31', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.dateRange.start).toBe('2024-01-01')
      expect(result.events).toBe(25000)
      expect(result.topCompanies).toHaveLength(2)
      expect(result.topBenefits).toHaveLength(2)
    })
  })

  describe('Real-time Analytics Monitoring', () => {
    it('should get real-time system health', async () => {
      const mockHealthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          database: { status: 'healthy', responseTime: 45 },
          cache: { status: 'healthy', hitRate: 0.87 },
          analytics: { status: 'healthy', queueSize: 12 },
          api: { status: 'healthy', requestsPerMinute: 150 }
        },
        alerts: [],
        uptime: 99.98
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockHealthData
      })

      const response = await fetch('/api/admin/analytics/health', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.status).toBe('healthy')
      expect(result.services.database.status).toBe('healthy')
      expect(result.services.cache.hitRate).toBe(0.87)
      expect(result.uptime).toBe(99.98)
      expect(result.alerts).toHaveLength(0)
    })

    it('should handle system health with warnings', async () => {
      const mockHealthWithWarnings = {
        status: 'warning',
        timestamp: new Date().toISOString(),
        services: {
          database: { status: 'healthy', responseTime: 45 },
          cache: { status: 'warning', hitRate: 0.65 },
          analytics: { status: 'healthy', queueSize: 85 },
          api: { status: 'healthy', requestsPerMinute: 150 }
        },
        alerts: [
          {
            type: 'warning',
            service: 'cache',
            message: 'Cache hit rate below threshold',
            threshold: 0.8,
            current: 0.65
          }
        ],
        uptime: 99.95
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockHealthWithWarnings
      })

      const response = await fetch('/api/admin/analytics/health', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.status).toBe('warning')
      expect(result.services.cache.status).toBe('warning')
      expect(result.alerts).toHaveLength(1)
      expect(result.alerts[0].type).toBe('warning')
    })

    it('should get live analytics stream', async () => {
      const mockLiveData = {
        currentUsers: 45,
        eventsLastMinute: 25,
        recentEvents: [
          {
            type: 'company_view',
            companyName: 'Tech Corp',
            timestamp: new Date().toISOString()
          },
          {
            type: 'search_query',
            query: 'remote work',
            timestamp: new Date().toISOString()
          }
        ],
        activePages: [
          { page: '/companies', users: 12 },
          { page: '/benefits', users: 8 },
          { page: '/dashboard', users: 6 }
        ]
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockLiveData
      })

      const response = await fetch('/api/admin/analytics/live', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.currentUsers).toBe(45)
      expect(result.eventsLastMinute).toBe(25)
      expect(result.recentEvents).toHaveLength(2)
      expect(result.activePages).toHaveLength(3)
    })
  })

  describe('Analytics Data Export and Backup', () => {
    it('should export analytics data', async () => {
      const exportRequest = {
        type: 'full_export',
        format: 'json',
        dateRange: {
          start: '2024-01-01',
          end: '2024-01-31'
        },
        includeRawData: true
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          exportId: 'export-123',
          status: 'processing',
          estimatedSize: '250MB',
          estimatedTime: '5 minutes'
        })
      })

      const response = await fetch('/api/admin/analytics/export', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(exportRequest)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.exportId).toBe('export-123')
      expect(result.status).toBe('processing')
      expect(result.estimatedSize).toBe('250MB')
    })

    it('should get export status', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          exportId: 'export-123',
          status: 'completed',
          downloadUrl: '/api/admin/exports/analytics-export-123.json',
          fileSize: '245MB',
          recordCount: 125000,
          completedAt: new Date().toISOString()
        })
      })

      const response = await fetch('/api/admin/analytics/export/export-123/status', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.status).toBe('completed')
      expect(result.downloadUrl).toContain('analytics-export-123.json')
      expect(result.recordCount).toBe(125000)
    })

    it('should create analytics backup', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          backupId: 'backup-456',
          message: 'Analytics backup created successfully',
          backupSize: '1.2GB',
          backupLocation: 's3://backups/analytics-backup-456.tar.gz'
        })
      })

      const response = await fetch('/api/admin/analytics/backup', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.backupId).toBe('backup-456')
      expect(result.backupSize).toBe('1.2GB')
    })
  })

  describe('Analytics Configuration Management', () => {
    it('should get analytics configuration', async () => {
      const mockConfig = {
        dataRetention: {
          rawEvents: 90, // days
          aggregatedData: 365, // days
          userActivity: 180 // days
        },
        sampling: {
          enabled: true,
          rate: 0.1 // 10% sampling
        },
        alerts: {
          errorRateThreshold: 0.05,
          responseTimeThreshold: 500,
          cacheHitRateThreshold: 0.8
        },
        features: {
          realTimeTracking: true,
          detailedUserTracking: false,
          geolocationTracking: false
        }
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockConfig
      })

      const response = await fetch('/api/admin/analytics/config', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.dataRetention.rawEvents).toBe(90)
      expect(result.sampling.enabled).toBe(true)
      expect(result.alerts.errorRateThreshold).toBe(0.05)
      expect(result.features.realTimeTracking).toBe(true)
    })

    it('should update analytics configuration', async () => {
      const configUpdate = {
        dataRetention: {
          rawEvents: 120,
          aggregatedData: 730
        },
        alerts: {
          errorRateThreshold: 0.03
        }
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Analytics configuration updated',
          updatedFields: ['dataRetention.rawEvents', 'dataRetention.aggregatedData', 'alerts.errorRateThreshold']
        })
      })

      const response = await fetch('/api/admin/analytics/config', {
        method: 'PATCH',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(configUpdate)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.updatedFields).toHaveLength(3)
    })
  })

  describe('Authorization and Security', () => {
    it('should require admin authorization for analytics management', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ 
          error: 'Authentication required' 
        })
      })

      const response = await fetch('/api/admin/analytics/dashboard')

      const result = await response.json()

      expect(response.status).toBe(401)
      expect(result.error).toBe('Authentication required')
    })

    it('should require super admin role for reset operations', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: async () => ({ 
          error: 'Super admin access required for analytics reset' 
        })
      })

      const response = await fetch('/api/admin/analytics/reset?type=all', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(403)
      expect(result.error).toBe('Super admin access required for analytics reset')
    })
  })
})
