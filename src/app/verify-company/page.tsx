import { Suspense } from 'react'
import { CompanyVerification } from '@/components/company-verification'
import { Loader2 } from 'lucide-react'

function VerificationFallback() {
  return (
    <div className="bg-white shadow-lg rounded-lg p-8">
      <div className="text-center">
        <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Loading...
        </h2>
        <p className="text-gray-600">
          Preparing verification process.
        </p>
      </div>
    </div>
  )
}

export default function VerifyCompanyPage() {

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Company Verification
          </h1>
          <p className="text-gray-600">
            Verifying your company association
          </p>
        </div>

        <Suspense fallback={<VerificationFallback />}>
          <CompanyVerification />
        </Suspense>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team.
          </p>
        </div>
      </div>
    </div>
  )
}
