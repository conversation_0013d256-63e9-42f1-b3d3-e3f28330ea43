import { NextRequest, NextResponse } from 'next/server'
import { verifyMagic<PERSON>inkToken, createSession } from '@/lib/magic-link-auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token } = body

    console.log('🔗 Magic link verification attempt:', { token: token?.substring(0, 8) + '...' })

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      )
    }

    // Verify magic link token
    const user = await verifyMagicLinkToken(token)

    console.log('✅ Magic link verification successful:', user.email)

    // Create session
    await createSession(user.id)

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      }
    })

  } catch (error) {
    console.error('Magic link verification error:', error)

    if (error instanceof Error) {
      if (error.message === 'Invalid or expired magic link' || 
          error.message === 'Magic link has expired') {
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        )
      }
      
      if (error.message === 'User not found') {
        return NextResponse.json(
          { error: 'User account not found' },
          { status: 404 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to verify magic link' },
      { status: 500 }
    )
  }
}
