/**
 * Global teardown for E2E tests
 * Cleans up test database and environment
 */

import { config } from 'dotenv'
import { query } from '@/lib/local-db'

// Load environment variables
config({ path: '.env.local' })

async function globalTeardown() {
  console.log('🎭 Starting E2E test teardown...')

  try {
    // Clean up E2E test data
    await cleanupE2EDatabase()

    console.log('✅ E2E test teardown complete')
  } catch (error) {
    console.error('❌ E2E test teardown failed:', error)
    // Don't throw - teardown failures shouldn't break the build
  }
}

async function cleanupE2EDatabase() {
  console.log('🎭 Cleaning up E2E test database...')

  const cleanupQueries = [
    // Clear rate limiting data for e2e test emails
    'DELETE FROM magic_link_rate_limits WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
    'DELETE FROM rate_limits WHERE identifier LIKE \'%@e2e.test\' OR identifier LIKE \'%@%.e2e\'',
    // Clear test data
    'DELETE FROM magic_link_tokens WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
    'DELETE FROM user_benefit_rankings WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\')',
    'DELETE FROM company_benefits WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM company_locations WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM user_sessions WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\')',
    'DELETE FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
    'DELETE FROM companies WHERE domain LIKE \'%.e2e\'',
    'DELETE FROM benefits WHERE name LIKE \'E2E %\'',
    'DELETE FROM benefit_categories WHERE name LIKE \'e2e_%\'',
  ]

  for (const sql of cleanupQueries) {
    try {
      await query(sql)
    } catch (error) {
      console.warn('Cleanup query failed (may be expected):', sql, error)
    }
  }

  console.log('🎭 E2E test database cleanup complete')
}



export default globalTeardown
