'use client'

import { useState, useEffect } from 'react'
import { Heart, HeartOff } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SaveCompanyButtonProps {
  companyId: string
  companyName: string
}

export function SaveCompanyButton({ companyId, companyName: _companyName }: SaveCompanyButtonProps) {
  const [isSaved, setIsSaved] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)

  // Check authentication status and saved status on component mount
  useEffect(() => {
    const checkAuthAndSavedStatus = async () => {
      try {
        // Check authentication first
        const authResponse = await fetch('/api/auth/me')
        const isAuth = authResponse.ok
        setIsAuthenticated(isAuth)

        // Only check saved status if authenticated
        if (isAuth) {
          const savedResponse = await fetch(`/api/saved-companies/${companyId}`)
          if (savedResponse.ok) {
            const data = await savedResponse.json()
            setIsSaved(data.saved)
          }
        }
      } catch (error) {
        console.error('Error checking auth/saved status:', error)
        setIsAuthenticated(false)
      }
    }

    checkAuthAndSavedStatus()
  }, [companyId])

  const handleSaveToggle = async () => {
    // If not authenticated, redirect to sign in
    if (isAuthenticated === false) {
      window.location.href = '/sign-in'
      return
    }

    setIsLoading(true)

    try {
      if (isSaved) {
        // Remove from saved companies
        const response = await fetch(`/api/saved-companies?companyId=${companyId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setIsSaved(false)
        } else {
          const error = await response.json()
          alert(error.error || 'Failed to remove company from saved list')
        }
      } else {
        // Add to saved companies
        const response = await fetch('/api/saved-companies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ companyId })
        })

        if (response.ok) {
          setIsSaved(true)
        } else {
          const error = await response.json()
          if (response.status === 401) {
            alert('Please sign in to save companies')
          } else {
            alert(error.error || 'Failed to save company')
          }
        }
      }
    } catch (error) {
      console.error('Error saving company:', error)
      alert('Failed to save company')
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading state while checking authentication
  if (isAuthenticated === null) {
    return (
      <Button
        variant="outline"
        disabled={true}
        className="flex items-center justify-center gap-2 w-full sm:w-auto"
      >
        <HeartOff className="w-4 h-4" />
        <span className="text-sm sm:text-base">Loading...</span>
      </Button>
    )
  }

  // Show sign in prompt for unauthenticated users
  if (isAuthenticated === false) {
    return (
      <Button
        variant="outline"
        onClick={handleSaveToggle}
        className="flex items-center justify-center gap-2 w-full sm:w-auto text-blue-600 border-blue-300 hover:bg-blue-50"
      >
        <HeartOff className="w-4 h-4" />
        <span className="text-sm sm:text-base">Sign in to Save</span>
      </Button>
    )
  }

  // Show normal save/saved state for authenticated users
  return (
    <Button
      variant="outline"
      onClick={handleSaveToggle}
      disabled={isLoading}
      className={`flex items-center justify-center gap-2 w-full sm:w-auto ${isSaved ? 'text-red-600 border-red-300 hover:bg-red-50' : ''}`}
    >
      {isSaved ? (
        <>
          <Heart className="w-4 h-4 fill-current" />
          <span className="text-sm sm:text-base">Saved</span>
        </>
      ) : (
        <>
          <HeartOff className="w-4 h-4" />
          <span className="text-sm sm:text-base">Save Company</span>
        </>
      )}
    </Button>
  )
}
