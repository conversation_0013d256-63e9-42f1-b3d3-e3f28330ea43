-- Schema Updates for PostgreSQL Cache System Compatibility
-- This migration adds missing columns and updates constraints to match cache system expectations

-- Add missing columns to companies table
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS website VARCHAR(500),
ADD COLUMN IF NOT EXISTS logo_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS founded_year INTEGER,
ADD COLUMN IF NOT EXISTS verification_status VARCHAR(50) DEFAULT 'pending';

-- Add check constraint for verification_status
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'companies_verification_status_check'
    ) THEN
        ALTER TABLE companies 
        ADD CONSTRAINT companies_verification_status_check 
        CHECK (verification_status IN ('pending', 'verified', 'rejected', 'suspended'));
    END IF;
END $$;

-- Create index on verification_status for performance
CREATE INDEX IF NOT EXISTS idx_companies_verification_status ON companies(verification_status);

-- Update activity_log event_type constraint to include cache-related events
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;

ALTER TABLE activity_log 
ADD CONSTRAINT activity_log_event_type_check 
CHECK (event_type IN (
    'company_added',
    'user_registered', 
    'benefit_verified',
    'benefit_disputed',
    'benefit_removal_dispute_submitted',
    'benefit_removal_dispute_approved',
    'benefit_removal_dispute_rejected',
    'benefit_removal_dispute_cancelled',
    'benefit_automatically_removed',
    'session_cleanup',
    'cache_refresh',
    'cache_maintenance',
    'user_login',
    'user_logout',
    'company_verified',
    'company_rejected'
));

-- Ensure uuid-ossp extension is available for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Update existing companies to have verified status if they have benefits
UPDATE companies 
SET verification_status = 'verified' 
WHERE id IN (
    SELECT DISTINCT company_id 
    FROM company_benefits 
    WHERE company_id IS NOT NULL
) AND verification_status = 'pending';

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update migration log
INSERT INTO migration_log (migration_name, description) VALUES
('003_update_schema_for_cache_system', 'Updated schema to support PostgreSQL cache system - added missing company columns and updated activity log constraints')
ON CONFLICT (migration_name) DO NOTHING;

-- Add some sample data for testing if tables are empty
DO $$
BEGIN
    -- Add some verification statuses to existing companies for testing
    IF (SELECT COUNT(*) FROM companies WHERE verification_status = 'verified') = 0 THEN
        -- Update first few companies to verified status for testing
        UPDATE companies 
        SET verification_status = 'verified' 
        WHERE id IN (
            SELECT id FROM companies 
            ORDER BY created_at 
            LIMIT 5
        );
    END IF;
END $$;

-- Create indexes for better performance on new columns
CREATE INDEX IF NOT EXISTS idx_companies_website ON companies(website) WHERE website IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_companies_founded_year ON companies(founded_year) WHERE founded_year IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN companies.website IS 'Company website URL';
COMMENT ON COLUMN companies.logo_url IS 'URL to company logo image';
COMMENT ON COLUMN companies.founded_year IS 'Year the company was founded';
COMMENT ON COLUMN companies.verification_status IS 'Company verification status: pending, verified, rejected, suspended';
