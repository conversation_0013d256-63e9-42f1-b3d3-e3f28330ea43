import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

/**
 * PUT /api/admin/missing-companies/[reportId]
 * Update missing company report status (approve/reject)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ reportId: string }> }
) {
  try {
    await requireAdmin()
    const { reportId } = await params
    const body = await request.json()
    const { status, adminNotes, companyId } = body

    // Normalize status (accept 'approved' as alias for 'added')
    const normalizedStatus = status === 'approved' ? 'added' : status

    if (!normalizedStatus || !['reviewed', 'added', 'rejected'].includes(normalizedStatus)) {
      return NextResponse.json(
        { error: 'Status must be one of: reviewed, added, rejected (or approved)' },
        { status: 400 }
      )
    }

    // Check if report exists
    const existingResult = await query(
      'SELECT id FROM missing_company_reports WHERE id = $1',
      [reportId]
    )

    if (existingResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Missing company report not found' },
        { status: 404 }
      )
    }

    // Update report status
    const result = await query(
      `UPDATE missing_company_reports
       SET status = $1, admin_notes = $2, company_id = $3, updated_at = NOW()
       WHERE id = $4
       RETURNING *`,
      [normalizedStatus, adminNotes || null, companyId || null, reportId]
    )

    return NextResponse.json({
      success: true,
      report: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating missing company report:', error)

    // Check if it's an admin access error
    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update missing company report' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/admin/missing-companies/[reportId]
 * Get specific missing company report details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ reportId: string }> }
) {
  try {
    await requireAdmin()
    const { reportId } = await params

    const result = await query(
      `SELECT 
        mcr.*,
        c.name as company_name,
        c.domain as company_domain
       FROM missing_company_reports mcr
       LEFT JOIN companies c ON mcr.company_id = c.id
       WHERE mcr.id = $1`,
      [reportId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Missing company report not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(result.rows[0])

  } catch (error) {
    console.error('Error retrieving missing company report:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve missing company report' },
      { status: 500 }
    )
  }
}
