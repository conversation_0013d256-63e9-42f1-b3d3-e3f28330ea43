#!/bin/bash
# PostgreSQL Cache Maintenance Cron Job
# Add this to your crontab: 0 * * * * /path/to/this/script

cd "$(dirname "$0")/.."
export NODE_ENV=production

# Run cache cleanup every hour
node scripts/session-cleanup.js

# Refresh materialized views every 6 hours (adjust as needed)
if [ $(($(date +%H) % 6)) -eq 0 ]; then
  node -e "import('./src/lib/postgresql-cache.js').then(m => m.refreshCacheViews()).then(() => console.log('Cache views refreshed'))"
fi
