import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin } from '@/lib/auth'

// GET /api/admin/benefit-categories/[categoryId] - Get a specific benefit category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin()
    const { categoryId } = await params
    
    const result = await query(`
      SELECT 
        bc.*,
        COUNT(b.id) as benefit_count
      FROM benefit_categories bc
      LEFT JOIN benefits b ON bc.id = b.category_id
      WHERE bc.id = $1
      GROUP BY bc.id
    `, [categoryId])
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit category not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(result.rows[0])
    
  } catch (error) {
    console.error('Error fetching benefit category:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefit category' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/benefit-categories/[categoryId] - Update a benefit category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin()
    const { categoryId } = await params
    const body = await request.json()
    const { name, display_name, description, icon, sort_order, is_active } = body
    
    // Check if category exists
    const existingCategory = await query(
      'SELECT * FROM benefit_categories WHERE id = $1',
      [categoryId]
    )
    
    if (existingCategory.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit category not found' },
        { status: 404 }
      )
    }
    
    const category = existingCategory.rows[0]
    
    // Validation
    if (!name || !display_name) {
      return NextResponse.json(
        { error: 'Name and display name are required' },
        { status: 400 }
      )
    }
    
    // Validate name format (lowercase, alphanumeric, underscores only)
    if (!/^[a-z0-9_]+$/.test(name)) {
      return NextResponse.json(
        { error: 'Name must contain only lowercase letters, numbers, and underscores' },
        { status: 400 }
      )
    }
    
    // Check if name already exists (excluding current category)
    if (name !== category.name) {
      const nameCheck = await query(
        'SELECT id FROM benefit_categories WHERE name = $1 AND id != $2',
        [name, categoryId]
      )
      
      if (nameCheck.rows.length > 0) {
        return NextResponse.json(
          { error: 'A category with this name already exists' },
          { status: 409 }
        )
      }
    }
    

    
    const result = await query(
      `UPDATE benefit_categories
       SET name = $1, display_name = $2, description = $3, icon = $4,
           sort_order = $5, is_active = $6, updated_at = NOW()
       WHERE id = $7 RETURNING *`,
      [name, display_name, description, icon, sort_order, is_active, categoryId]
    )

    // Note: No need to update legacy category column since it was removed

    return NextResponse.json(result.rows[0])
    
  } catch (error) {
    console.error('Error updating benefit category:', error)
    return NextResponse.json(
      { error: 'Failed to update benefit category' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/benefit-categories/[categoryId] - Delete a benefit category
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin()
    const { categoryId } = await params
    
    // Check if category exists
    const existingCategory = await query(
      'SELECT * FROM benefit_categories WHERE id = $1',
      [categoryId]
    )
    
    if (existingCategory.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit category not found' },
        { status: 404 }
      )
    }
    
    const _category = existingCategory.rows[0]
    

    
    // Check if category has benefits assigned
    const benefitCount = await query(
      'SELECT COUNT(*) as count FROM benefits WHERE category_id = $1',
      [categoryId]
    )
    
    if (parseInt(benefitCount.rows[0].count) > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete category with assigned benefits',
          details: `This category has ${benefitCount.rows[0].count} benefits assigned to it. Please reassign or delete these benefits first.`
        },
        { status: 400 }
      )
    }
    
    await query('DELETE FROM benefit_categories WHERE id = $1', [categoryId])
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('Error deleting benefit category:', error)
    return NextResponse.json(
      { error: 'Failed to delete benefit category' },
      { status: 500 }
    )
  }
}
