import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Global ignores (replaces .eslintignore)
  {
    ignores: [
      "node_modules/**",
      ".next/**",
      "out/**",
      "dist/**",
      "build/**",
      "coverage/**",
      "*.tsbuildinfo",
      "next-env.d.ts",
      ".env*",
      "*.log",
      "*.db",
      "*.sqlite",
      "backups/**",
      "k8s/**",
      "scripts/**",
      "data/**",
      "docs/**",
      "*.md",
      "next.config.js",
      "postcss.config.mjs",
      "tailwind.config.js",
      "vitest.config.ts",
      "vitest.setup.ts",
      "fix-eslint-errors.cjs",
      "fix-variables.cjs"
    ]
  },
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // TypeScript rules - enforce strict typing
      "@typescript-eslint/no-explicit-any": "warn", // Changed from warn to error
      "@typescript-eslint/no-unused-vars": ["warn", { // Changed from warn to error
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_"
      }],
      "@typescript-eslint/no-require-imports": "warn", // Changed from warn to error

      // JavaScript/ES6 rules
      "prefer-const": "warn", // Changed from warn to error
      "no-var": "error",
      "no-use-before-define": ["warn", { // NEW: Prevent TDZ errors
        "functions": true,
        "classes": true,
        "variables": true,
        "allowNamedExports": false
      }],

      // React rules - enforce best practices
      "react-hooks/exhaustive-deps": "warn", // Changed from warn to error
      "react/no-unescaped-entities": "warn", // Changed from warn to error
      "react/jsx-key": "error",
      "react/jsx-no-duplicate-props": "error",
      "react/jsx-no-undef": "error",
      "react/no-direct-mutation-state": "error",
      "react/no-unknown-property": "error",

      // Console and debugging rules
      "no-console": "off", // Allow console for server-side logging
      "no-debugger": "error",
      "no-alert": "off", // Allow alerts for user feedback in admin interfaces

      // Code quality rules
      "no-duplicate-imports": "error",
      "no-unreachable": "error",
      "no-unused-expressions": "warn",
      "eqeqeq": ["error", "always"],
      "curly": ["warn", "all"],
    },
  },
];

export default eslintConfig;
