// PostgreSQL-only session storage that works in both Node.js and Edge Runtime
import { query } from './local-db'

export interface SessionData {
  userId: string
  expiresAt: string
  createdAt: string
}

// PostgreSQL session management (primary and only storage)
async function getPostgreSQLSession(sessionToken: string): Promise<SessionData | null> {
  try {
    const result = await query(
      `SELECT u.id as user_id, us.expires_at, us.created_at
       FROM user_sessions us
       JOIN users u ON us.user_id = u.id
       WHERE us.session_token = $1 AND us.expires_at > NOW()`,
      [sessionToken]
    )

    if (result.rows.length === 0) {
      return null
    }

    const session = result.rows[0]
    return {
      userId: session.user_id,
      expiresAt: session.expires_at.toISOString(),
      createdAt: session.created_at.toISOString(),
    }
  } catch (error) {
    console.error('Error getting session from PostgreSQL:', error)
    return null
  }
}

async function setPostgreSQLSession(sessionToken: string, userId: string, expiresAt: Date): Promise<boolean> {
  try {
    const result = await query(
      `INSERT INTO user_sessions (user_id, session_token, expires_at)
       VALUES ($1, $2, $3)
       ON CONFLICT (session_token)
       DO UPDATE SET expires_at = EXCLUDED.expires_at, user_id = EXCLUDED.user_id
       RETURNING id`,
      [userId, sessionToken, expiresAt]
    )

    return result.rows.length > 0
  } catch (error) {
    console.error('Error setting session in PostgreSQL:', error)
    return false
  }
}

async function deletePostgreSQLSession(sessionToken: string): Promise<boolean> {
  try {
    const result = await query(
      'DELETE FROM user_sessions WHERE session_token = $1',
      [sessionToken]
    )

    return result.rowCount > 0
  } catch (error) {
    console.error('Error deleting session from PostgreSQL:', error)
    return false
  }
}



// Public API - PostgreSQL only
export async function getSession(sessionToken: string): Promise<SessionData | null> {
  return await getPostgreSQLSession(sessionToken)
}

export async function setSession(sessionToken: string, userId: string, expiresAt: Date): Promise<boolean> {
  return await setPostgreSQLSession(sessionToken, userId, expiresAt)
}

export async function deleteSession(sessionToken: string): Promise<boolean> {
  return await deletePostgreSQLSession(sessionToken)
}

export async function deleteAllUserSessions(userId: string): Promise<boolean> {
  try {
    // Delete from PostgreSQL only
    const _result = await query('DELETE FROM user_sessions WHERE user_id = $1', [userId])
    return true
  } catch (error) {
    console.error('Error deleting all user sessions:', error)
    return false
  }
}

// Health check function
export async function checkSessionStorageHealth(): Promise<{
  postgresql: boolean
}> {
  let postgresqlHealthy = false

  // Check PostgreSQL
  try {
    await query('SELECT 1')
    postgresqlHealthy = true
  } catch {
    // PostgreSQL not available
  }

  return {
    postgresql: postgresqlHealthy
  }
}
