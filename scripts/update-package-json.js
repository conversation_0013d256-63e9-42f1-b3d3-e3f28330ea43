#!/usr/bin/env node

// Script to update package.json and remove Redis dependencies

import fs from 'fs'
import path from 'path'

const packageJsonPath = path.join(process.cwd(), 'package.json')

try {
  // Read package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  
  // Remove Redis dependencies
  const redisPackages = ['redis', '@types/redis']
  let removedPackages = []
  
  redisPackages.forEach(pkg => {
    if (packageJson.dependencies && packageJson.dependencies[pkg]) {
      delete packageJson.dependencies[pkg]
      removedPackages.push(pkg)
    }
    if (packageJson.devDependencies && packageJson.devDependencies[pkg]) {
      delete packageJson.devDependencies[pkg]
      removedPackages.push(pkg)
    }
  })
  
  // Update scripts to remove Redis references
  if (packageJson.scripts) {
    // Remove Redis-specific scripts
    const redisScripts = ['redis:cli', 'redis:monitor', 'redis:flushall']
    redisScripts.forEach(script => {
      if (packageJson.scripts[script]) {
        delete packageJson.scripts[script]
        removedPackages.push(`script: ${script}`)
      }
    })
    
    // Update dev:start script to not include Redis
    if (packageJson.scripts['dev:start']) {
      packageJson.scripts['dev:start'] = packageJson.scripts['dev:start']
        .replace(/redis/gi, '')
        .replace(/&&\s*&&/g, '&&')
        .replace(/^\s*&&\s*/, '')
        .replace(/\s*&&\s*$/, '')
        .trim()
    }
  }
  
  // Add new scripts for PostgreSQL cache management
  if (!packageJson.scripts['cache:cleanup']) {
    packageJson.scripts['cache:cleanup'] = 'node scripts/session-cleanup.js'
  }
  
  if (!packageJson.scripts['cache:stats']) {
    packageJson.scripts['cache:stats'] = 'node -e "require(\'./src/lib/postgresql-cache\').getCacheStats().then(console.log)"'
  }
  
  if (!packageJson.scripts['cache:refresh']) {
    packageJson.scripts['cache:refresh'] = 'node -e "require(\'./src/lib/postgresql-cache\').refreshCacheViews().then(() => console.log(\'Cache refreshed\'))"'
  }
  
  // Write updated package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n')
  
  console.log('✅ package.json updated successfully!')
  if (removedPackages.length > 0) {
    console.log('📦 Removed:', removedPackages.join(', '))
  }
  console.log('🔧 Added cache management scripts')
  
} catch (error) {
  console.error('❌ Error updating package.json:', error.message)
  process.exit(1)
}
