#!/usr/bin/env node

/**
 * Company Benefits Import Script for BenefitLens
 * 
 * This script imports company-benefit relationships from a JSON file.
 * It maps company names to benefit names and creates the associations.
 * 
 * Usage:
 *   node scripts/import-company-benefits.js --file=data/company-benefits.json [options]
 * 
 * Options:
 *   --file: Path to JSON file containing company-benefit mappings [required]
 *   --dry-run: Preview changes without actually importing
 *   --overwrite: Remove existing benefits and replace with new ones
 *   --help, -h: Show this help message
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'benefitlens_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'benefitlens',
  password: process.env.DB_PASSWORD || 'benefitlens_password',
  port: process.env.DB_PORT || 5432,
});

// Logger utility
class Logger {
  constructor(isDryRun = false) {
    this.isDryRun = isDryRun;
    this.stats = {
      processed: 0,
      imported: 0,
      skipped: 0,
      errors: 0
    };
  }

  info(message) {
    console.log(`[INFO] ${message}`);
  }

  success(message) {
    console.log(`[SUCCESS] ${message}`);
  }

  warn(message) {
    console.log(`[WARN] ${message}`);
  }

  error(message) {
    console.log(`[ERROR] ${message}`);
  }

  dryRun(message) {
    console.log(`[DRY-RUN] ${message}`);
  }
}

// Parse command line arguments
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    file: null,
    dryRun: false,
    overwrite: false,
    unverified: false,
    help: false
  };

  for (const arg of args) {
    if (arg === '--help' || arg === '-h') {
      options.help = true;
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--overwrite') {
      options.overwrite = true;
    } else if (arg === '--unverified') {
      options.unverified = true;
    } else if (arg.startsWith('--file=')) {
      options.file = arg.split('=')[1];
    }
  }

  return options;
}

// Show help message
function showHelp() {
  console.log(`
Company Benefits Import Script for BenefitLens

Usage:
  node scripts/import-company-benefits.js --file=data/company-benefits.json [options]

Options:
  --file=FILE        Path to JSON file containing company-benefit mappings [required]
  --dry-run          Preview changes without actually importing
  --overwrite        Remove existing benefits and replace with new ones
  --unverified       Add benefits as unverified (default: verified)
  --help, -h         Show this help message

File Format:
  The JSON file should contain an array of objects with the following structure:
  [
    {
      "company_name": "Company Name",
      "benefits": ["Benefit Name 1", "Benefit Name 2", ...]
    },
    ...
  ]

Examples:
  node scripts/import-company-benefits.js --file=data/german-stock-indices-company-benefits.json
  node scripts/import-company-benefits.js --file=data/company-benefits.json --dry-run
  node scripts/import-company-benefits.js --file=data/company-benefits.json --overwrite
  node scripts/import-company-benefits.js --file=data/company-benefits.json --unverified
`);
}

// Load and validate JSON file
function loadDataFile(filePath) {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }

  const fileContent = fs.readFileSync(filePath, 'utf8');
  
  try {
    const data = JSON.parse(fileContent);
    
    if (!Array.isArray(data)) {
      throw new Error('JSON file must contain an array of company-benefit mappings');
    }

    // Validate structure
    for (const item of data) {
      if (!item.company_name || !item.benefits || !Array.isArray(item.benefits)) {
        throw new Error('Each item must have "company_name" and "benefits" array');
      }
    }

    return data;
  } catch (error) {
    throw new Error(`Failed to parse JSON file: ${error.message}`);
  }
}

// Get company ID by name
async function getCompanyId(companyName) {
  const client = await pool.connect();
  try {
    const query = 'SELECT id FROM companies WHERE LOWER(name) = LOWER($1)';
    const result = await client.query(query, [companyName]);
    return result.rows.length > 0 ? result.rows[0].id : null;
  } finally {
    client.release();
  }
}

// Get benefit ID by name
async function getBenefitId(benefitName) {
  const client = await pool.connect();
  try {
    const query = 'SELECT id FROM benefits WHERE LOWER(name) = LOWER($1)';
    const result = await client.query(query, [benefitName]);
    return result.rows.length > 0 ? result.rows[0].id : null;
  } finally {
    client.release();
  }
}

// Get all benefits as a map for faster lookup
async function getBenefitsMap() {
  const client = await pool.connect();
  try {
    const query = 'SELECT id, name FROM benefits';
    const result = await client.query(query);
    const benefitsMap = new Map();
    result.rows.forEach(row => {
      benefitsMap.set(row.name.toLowerCase(), row.id);
    });
    return benefitsMap;
  } finally {
    client.release();
  }
}

// Get all companies as a map for faster lookup
async function getCompaniesMap() {
  const client = await pool.connect();
  try {
    const query = 'SELECT id, name FROM companies';
    const result = await client.query(query);
    const companiesMap = new Map();
    result.rows.forEach(row => {
      companiesMap.set(row.name.toLowerCase(), row.id);
    });
    return companiesMap;
  } finally {
    client.release();
  }
}

// Remove existing company benefits (for overwrite mode)
async function removeExistingBenefits(companyId, logger) {
  if (logger.isDryRun) {
    logger.dryRun(`Would remove existing benefits for company ${companyId}`);
    return;
  }

  const client = await pool.connect();
  try {
    const query = 'DELETE FROM company_benefits WHERE company_id = $1';
    const result = await client.query(query, [companyId]);
    logger.info(`Removed ${result.rowCount} existing benefits for company`);
  } finally {
    client.release();
  }
}

// Add company benefit relationship
async function addCompanyBenefit(companyId, benefitId, logger, isVerified = true) {
  if (logger.isDryRun) {
    const verificationStatus = isVerified ? 'verified' : 'unverified';
    logger.dryRun(`Would add ${verificationStatus} benefit ${benefitId} to company ${companyId}`);
    return true;
  }

  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)
      VALUES ($1, $2, $3, 'import-script')
      ON CONFLICT (company_id, benefit_id) DO NOTHING
      RETURNING id
    `;
    const result = await client.query(query, [companyId, benefitId, isVerified]);
    return result.rows.length > 0;
  } finally {
    client.release();
  }
}

// Import company benefits
async function importCompanyBenefits(data, logger, overwrite = false, unverified = false) {
  const verificationStatus = unverified ? 'unverified' : 'verified';
  logger.info(`Starting import of company benefits for ${data.length} companies (${verificationStatus})...`);

  // Get lookup maps for better performance
  const companiesMap = await getCompaniesMap();
  const benefitsMap = await getBenefitsMap();

  for (const companyData of data) {
    logger.stats.processed++;

    try {
      const companyName = companyData.company_name;
      const companyId = companiesMap.get(companyName.toLowerCase());

      if (!companyId) {
        logger.error(`Company not found: ${companyName}`);
        logger.stats.errors++;
        continue;
      }

      // Remove existing benefits if overwrite mode
      if (overwrite) {
        await removeExistingBenefits(companyId, logger);
      }

      let addedCount = 0;
      let skippedCount = 0;

      for (const benefitName of companyData.benefits) {
        const benefitId = benefitsMap.get(benefitName.toLowerCase());

        if (!benefitId) {
          logger.warn(`Benefit not found: ${benefitName} (for company: ${companyName})`);
          skippedCount++;
          continue;
        }

        const added = await addCompanyBenefit(companyId, benefitId, logger, !unverified);
        if (added) {
          addedCount++;
        } else {
          skippedCount++;
        }
      }

      logger.success(`${companyName}: Added ${addedCount} benefits, skipped ${skippedCount}`);
      logger.stats.imported += addedCount;
      logger.stats.skipped += skippedCount;

    } catch (error) {
      logger.error(`Failed to process company ${companyData.company_name}: ${error.message}`);
      logger.stats.errors++;
    }
  }
}

// Main function
async function main() {
  try {
    const options = parseArguments();

    if (options.help) {
      showHelp();
      process.exit(0);
    }

    if (!options.file) {
      console.error('[ERROR] --file parameter is required');
      showHelp();
      process.exit(1);
    }

    const logger = new Logger(options.dryRun);

    logger.info('Company Benefits Import Script for BenefitLens');
    logger.info(`File: ${options.file}`);
    logger.info(`Dry run: ${options.dryRun}`);
    logger.info(`Overwrite: ${options.overwrite}`);
    logger.info(`Unverified: ${options.unverified}`);

    // Load data file
    logger.info('Loading data file...');
    const data = loadDataFile(options.file);
    logger.info(`Loaded ${data.length} company-benefit mappings from file`);

    // Import company benefits
    await importCompanyBenefits(data, logger, options.overwrite, options.unverified);

    // Print statistics
    console.log('\n=== Import Statistics ===');
    console.log(`Processed: ${logger.stats.processed}`);
    console.log(`Imported: ${logger.stats.imported}`);
    console.log(`Skipped: ${logger.stats.skipped}`);
    console.log(`Errors: ${logger.stats.errors}`);

    logger.info('Import completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error(`[FATAL] ${error.message}`);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
