import { NextRequest, NextResponse } from 'next/server'
import { getCompanyLocations, addCompanyLocation, updateCompanyLocation, removeCompanyLocation } from '@/lib/database'
import { requireAuth, canUserManageCompany } from '@/lib/auth'
import type { LocationType } from '@/types/database'

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    const locations = await getCompanyLocations(companyId)
    return NextResponse.json(locations)
  } catch (error) {
    console.error('Error fetching company locations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company locations' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const _userId = await requireAuth()
    const { id: companyId } = await params

    // Check if user can manage this company
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'Not authorized to manage this company' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { location_raw, location_type = 'office', is_primary = false, is_headquarters = false } = body

    if (!location_raw) {
      return NextResponse.json(
        { error: 'Location is required' },
        { status: 400 }
      )
    }

    const location = await addCompanyLocation(
      companyId,
      location_raw,
      location_type as LocationType,
      is_primary,
      is_headquarters
    )

    return NextResponse.json(location, { status: 201 })
  } catch (error) {
    console.error('Error adding company location:', error)
    return NextResponse.json(
      { error: 'Failed to add company location' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const _userId = await requireAuth()
    const { id: companyId } = await params
    
    // Check if user can manage this company
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'Not authorized to manage this company' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { location_id, ...updates } = body

    if (!location_id) {
      return NextResponse.json(
        { error: 'Location ID is required' },
        { status: 400 }
      )
    }

    const location = await updateCompanyLocation(location_id, updates)
    return NextResponse.json(location)
  } catch (error) {
    console.error('Error updating company location:', error)
    return NextResponse.json(
      { error: 'Failed to update company location' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const _userId = await requireAuth()
    const { id: companyId } = await params
    
    // Check if user can manage this company
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'Not authorized to manage this company' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get('location_id')

    if (!locationId) {
      return NextResponse.json(
        { error: 'Location ID is required' },
        { status: 400 }
      )
    }

    await removeCompanyLocation(locationId)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error removing company location:', error)
    return NextResponse.json(
      { error: 'Failed to remove company location' },
      { status: 500 }
    )
  }
}
