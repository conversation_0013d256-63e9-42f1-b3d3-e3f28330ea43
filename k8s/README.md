# Kubernetes Deployment for BenefitLens

This directory contains Kubernetes manifests for deploying BenefitLens in a self-hosted environment.

## Prerequisites

- Kubernetes cluster (v1.20+)
- kubectl configured to access your cluster
- Docker registry access
- Ingress controller (nginx recommended)
- cert-manager (optional, for SSL certificates)

## Quick Start

1. **Build and push the Docker image:**
   ```bash
   # Set your registry
   export DOCKER_REGISTRY=your-registry.com
   export IMAGE_TAG=v1.0.0
   
   # Run the deployment script
   ./scripts/deploy-k8s.sh
   ```

2. **Initialize the database:**
   ```bash
   ./scripts/init-db-k8s.sh
   ```

3. **Access the application:**
   ```bash
   # Port forward for local access
   kubectl port-forward service/workwell 3000:80 -n workwell
   
   # Or configure your ingress with your domain
   ```

## Manual Deployment

If you prefer to deploy manually:

1. **Create namespace:**
   ```bash
   kubectl apply -f namespace.yaml
   ```

2. **Deploy configuration:**
   ```bash
   kubectl apply -f configmap.yaml
   kubectl apply -f secret.yaml
   ```

3. **Deploy PostgreSQL:**
   ```bash
   kubectl apply -f postgres.yaml
   ```



5. **Deploy the application:**
   ```bash
   # Update the image in workwell.yaml first
   kubectl apply -f workwell.yaml
   ```

## Configuration

### Environment Variables

Edit `configmap.yaml` to customize:
- `DATABASE_URL`: PostgreSQL connection string
- `CACHE_TYPE`: Cache type (set to "postgresql")
- `SMTP_HOST`: Email server host
- `SMTP_PORT`: Email server port

### Secrets

Edit `secret.yaml` to set:
- `SESSION_SECRET`: Session encryption key
- `NEXT_PUBLIC_APP_URL`: Your application URL

**Important:** Base64 encode your secrets:
```bash
echo -n "your-secret" | base64
```

### Ingress

Update `benefitlens.yaml` ingress section:
- Replace `your-domain.com` with your actual domain
- Configure SSL certificates if using cert-manager

## Storage

The deployment uses PersistentVolumeClaims for data persistence:
- PostgreSQL: 10Gi


Adjust storage sizes in the respective YAML files based on your needs.

## Monitoring

Check deployment status:
```bash
# View all resources
kubectl get all -n workwell

# Check pod logs
kubectl logs -f deployment/workwell -n workwell
kubectl logs -f deployment/postgres -n workwell
kubectl logs -f deployment/redis -n workwell

# Check pod status
kubectl describe pod <pod-name> -n workwell
```

## Scaling

Scale the application:
```bash
kubectl scale deployment workwell --replicas=5 -n workwell
```

## Backup

Backup PostgreSQL data:
```bash
kubectl exec -n workwell deployment/postgres -- pg_dump -U workwell_user workwell > backup.sql
```

## Troubleshooting

### Common Issues

1. **Pods not starting:**
   - Check resource limits
   - Verify image availability
   - Check logs: `kubectl logs <pod-name> -n workwell`

2. **Database connection errors:**
   - Verify PostgreSQL is running
   - Check connection string in configmap
   - Ensure network policies allow communication

3. **Image pull errors:**
   - Verify registry credentials
   - Check image name and tag
   - Ensure registry is accessible from cluster

### Health Checks

The application includes health check endpoints:
- `/api/health`: Application health
- `/api/health/simple`: Simple health check

## Security

The deployment includes security best practices:
- Non-root containers
- Security contexts
- Resource limits
- Network policies (configure as needed)

## Updates

To update the application:
1. Build new image with updated tag
2. Update image reference in `benefitlens.yaml`
3. Apply the changes: `kubectl apply -f benefitlens.yaml`
4. Monitor rollout: `kubectl rollout status deployment/benefitlens -n benefitlens`
