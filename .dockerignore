# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env*
!.env.example

# Vercel
.vercel

# Next.js
.next/
out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.vscode/
.idea/

# Testing
/coverage

# Git
.git
.gitignore

# Documentation
README.md
docs/

# Docker
Dockerfile
.dockerignore

# Kubernetes
k8s/

# Scripts
scripts/

# Database backups
backups/
