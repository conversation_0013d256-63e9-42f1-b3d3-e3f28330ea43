#!/usr/bin/env node

/**
 * Fix Benefit Names Script for BenefitLens
 * 
 * This script updates the company benefits JSON file to use the correct benefit names
 * that exist in the database instead of generic names.
 */

const fs = require('fs');
const path = require('path');

// Mapping from incorrect benefit names to correct database names
const benefitNameMapping = {
  // Health benefits
  "Health Insurance": "Mental Health Support (Psychologische Betreuung)", // Generic -> specific available
  "Dental Insurance": "Dental Insurance Plus (Zahnzusatzversicherung)",
  "Vision Insurance": "Company Health Check-ups (Betriebsärztliche Untersuchungen)", // Generic -> available
  "Mental Health Support": "Mental Health Support (Psychologische Betreuung)",
  
  // Gym/Fitness benefits - rotate through available options
  "Gym Membership": [
    "Gym Membership - EGYM Wellpass",
    "Gym Membership - Hansefit", 
    "Gym Membership - Other",
    "Gym Membership - Urban Sports Club",
    "Gym Membership - Wellhub"
  ],
  
  // Work-life balance
  "Flexible Working Hours": "Flexible Working Hours (Gleitzeit)",
  "Remote Work": "Remote Work (Homeoffice)",
  "Parental Leave": "Parental Leave (Elternzeit)",
  
  // Development
  "Conference Attendance": "Conference Attendance (Konferenz-Teilnahme)",
  
  // Other benefits
  "Company Car": "Company Car (Dienstwagen)",
  
  // Add bike leasing for companies that don't have it
  "_ADD_BIKE_LEASING": [
    "Bike Leasing (Dienstfahrrad) - JobRad",
    "Bike Leasing (Dienstfahrrad) - Deutsche Dienstrad", 
    "Bike Leasing (Dienstfahrrad) - Bikeleasing",
    "Bike Leasing (Dienstfahrrad) - Other"
  ]
};

function fixBenefitNames(filePath) {
  console.log(`Reading file: ${filePath}`);
  
  // Read the JSON file
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const companies = JSON.parse(fileContent);
  
  console.log(`Processing ${companies.length} companies...`);
  
  let gymMembershipIndex = 0;
  let bikeLeasingIndex = 0;
  
  // Process each company
  companies.forEach((company, companyIndex) => {
    const originalBenefits = [...company.benefits];
    const newBenefits = [];
    
    company.benefits.forEach(benefit => {
      if (benefitNameMapping[benefit]) {
        const mapping = benefitNameMapping[benefit];
        
        if (Array.isArray(mapping)) {
          // For gym membership, rotate through options
          if (benefit === "Gym Membership") {
            newBenefits.push(mapping[gymMembershipIndex % mapping.length]);
            gymMembershipIndex++;
          }
        } else {
          // Direct mapping
          newBenefits.push(mapping);
        }
      } else {
        // Keep the benefit as-is if no mapping found
        newBenefits.push(benefit);
      }
    });
    
    // Add bike leasing to companies that don't have it (every 3rd company)
    const hasBikeLeasing = newBenefits.some(b => b.includes('Bike Leasing'));
    if (!hasBikeLeasing && companyIndex % 3 === 0) {
      const bikeLeasingOptions = benefitNameMapping._ADD_BIKE_LEASING;
      newBenefits.push(bikeLeasingOptions[bikeLeasingIndex % bikeLeasingOptions.length]);
      bikeLeasingIndex++;
    }
    
    company.benefits = newBenefits;
    
    // Log changes
    const addedBenefits = newBenefits.filter(b => !originalBenefits.includes(b));
    const removedBenefits = originalBenefits.filter(b => !newBenefits.includes(b));
    
    if (addedBenefits.length > 0 || removedBenefits.length > 0) {
      console.log(`\n${company.company_name}:`);
      if (removedBenefits.length > 0) {
        console.log(`  Removed: ${removedBenefits.join(', ')}`);
      }
      if (addedBenefits.length > 0) {
        console.log(`  Added: ${addedBenefits.join(', ')}`);
      }
    }
  });
  
  // Write the updated JSON back to file
  const updatedContent = JSON.stringify(companies, null, 2);
  fs.writeFileSync(filePath, updatedContent, 'utf8');
  
  console.log(`\nUpdated file saved: ${filePath}`);
  console.log(`Processed ${companies.length} companies successfully!`);
}

// Main execution
function main() {
  const filePath = process.argv[2] || 'data/german-stock-indices-company-benefits.json';
  
  if (!fs.existsSync(filePath)) {
    console.error(`Error: File not found: ${filePath}`);
    process.exit(1);
  }
  
  try {
    fixBenefitNames(filePath);
    console.log('\n✅ Benefit names fixed successfully!');
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixBenefitNames };
