/**
 * Analytics System Tests
 * Tests for the analytics tracking system, API endpoints, and data integrity
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

// Test configuration
const BASE_URL = 'http://localhost:3000'
const _TEST_TIMEOUT = 30000

// Test data
const TEST_COMPANY_ID = 'test-company-123'
const _TEST_BENEFIT_ID = 'test-benefit-456'
const TEST_USER_ID = 'test-user-789'

// Helper functions
async function makeRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${BASE_URL}${endpoint}`
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  })

  const data = await response.json()
  return { response, data }
}

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const _mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('Analytics System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset fetch mock
    ;(mockFetch).mockClear()
  })

  describe('Analytics Tracking', () => {
    it('should track company views', async () => {
      // Mock successful tracking response
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const _response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'company_view',
          data: {
            companyId: TEST_COMPANY_ID,
            referrer: 'https://example.com'
          }
        })
      })

      expect(fetch).toHaveBeenCalledWith(
        `${BASE_URL}/api/analytics/track`,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({
            type: 'company_view',
            data: {
              companyId: TEST_COMPANY_ID,
              referrer: 'https://example.com'
            }
          })
        })
      )
    })

    it('should track benefit searches', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const _response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'benefit_search',
          data: {
            query: 'health insurance',
            results_count: 5
          }
        })
      })

      expect(fetch).toHaveBeenCalledWith(
        `${BASE_URL}/api/analytics/track`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            type: 'benefit_search',
            data: {
              query: 'health insurance',
              results_count: 5
            }
          })
        })
      )
    })

    it('should track user interactions', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      const _response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'user_interaction',
          data: {
            action: 'save_company',
            companyId: TEST_COMPANY_ID,
            userId: TEST_USER_ID
          }
        })
      })

      expect(fetch).toHaveBeenCalledWith(
        `${BASE_URL}/api/analytics/track`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            type: 'user_interaction',
            data: {
              action: 'save_company',
              companyId: TEST_COMPANY_ID,
              userId: TEST_USER_ID
            }
          })
        })
      )
    })
  })

  describe('Analytics Data Retrieval', () => {
    it('should retrieve analytics insights for paying users', async () => {
      const mockInsights = {
        totalViews: 1000,
        topBenefits: ['Health Insurance', 'Remote Work'],
        userEngagement: 85
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockInsights
      })

      const _response = await makeRequest('/api/analytics/insights')

      expect(_response.data).toEqual(mockInsights)
    })

    it('should return preview data for non-paying users', async () => {
      const mockPreviewData = {
        preview: true,
        message: 'Upgrade to see full analytics'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockPreviewData
      })

      const _response = await makeRequest('/api/analytics/insights?preview=true')

      expect(_response.data).toEqual(mockPreviewData)
    })
  })

  describe('Admin Analytics', () => {
    it('should allow admin to reset analytics data', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: 'Analytics data reset' })
      })

      const _response = await makeRequest('/api/admin/analytics/reset?type=all', {
        method: 'POST'
      })

      expect(_response.data.success).toBe(true)
    })

    it('should provide admin analytics dashboard data', async () => {
      const mockDashboardData = {
        totalUsers: 500,
        totalCompanies: 100,
        totalBenefits: 50,
        dailyActiveUsers: 25
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockDashboardData
      })

      const _response = await makeRequest('/api/admin/analytics/dashboard')

      expect(_response.data).toEqual(mockDashboardData)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid tracking data', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Invalid tracking data' })
      })

      const _response = await makeRequest('/api/analytics/track', {
        method: 'POST',
        body: JSON.stringify({
          type: 'invalid_type',
          data: {}
        })
      })

      expect(_response.response.ok).toBe(false)
      expect(_response.data.error).toBe('Invalid tracking data')
    })

    it('should handle unauthorized access to admin endpoints', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({ error: 'Unauthorized' })
      })

      const _response = await makeRequest('/api/admin/analytics/reset', {
        method: 'POST'
      })

      expect(_response.response.ok).toBe(false)
      expect(_response.data.error).toBe('Unauthorized')
    })
  })
})
