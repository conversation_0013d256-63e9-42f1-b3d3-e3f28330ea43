-- PostgreSQL Session Management Optimizations
-- This migration optimizes the session management for PostgreSQL-only usage

-- Add additional indexes for session performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_created_at ON user_sessions(created_at);

-- Composite index for efficient session cleanup
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_created ON user_sessions(expires_at, created_at);

-- Add a function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        metadata,
        created_at
    ) VALUES (
        'session_cleanup',
        'Automated cleanup of expired sessions',
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run session cleanup every hour
-- Note: This requires pg_cron extension or can be run via cron job
-- For now, we'll create the function and document the cron setup

-- Add a function to get active session count for monitoring
CREATE OR REPLACE FUNCTION get_active_session_stats()
RETURNS TABLE(
    total_sessions INTEGER,
    expired_sessions INTEGER,
    active_sessions INTEGER,
    oldest_session TIMESTAMP WITH TIME ZONE,
    newest_session TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_sessions,
        COUNT(*) FILTER (WHERE expires_at < NOW())::INTEGER as expired_sessions,
        COUNT(*) FILTER (WHERE expires_at >= NOW())::INTEGER as active_sessions,
        MIN(created_at) as oldest_session,
        MAX(created_at) as newest_session
    FROM user_sessions;
END;
$$ LANGUAGE plpgsql;

-- Add a function to delete all sessions for a specific user (equivalent to Redis deleteAllUserSessions)
CREATE OR REPLACE FUNCTION delete_all_user_sessions(target_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE user_id = target_user_id;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        user_id,
        metadata,
        created_at
    ) VALUES (
        'user_sessions_deleted',
        'All sessions deleted for user',
        target_user_id::VARCHAR,
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a view for session monitoring
CREATE OR REPLACE VIEW session_monitoring AS
SELECT 
    u.email,
    u.first_name,
    u.last_name,
    us.session_token,
    us.created_at,
    us.expires_at,
    CASE 
        WHEN us.expires_at < NOW() THEN 'expired'
        ELSE 'active'
    END as status,
    EXTRACT(EPOCH FROM (us.expires_at - NOW())) / 3600 as hours_until_expiry
FROM user_sessions us
JOIN users u ON us.user_id = u.id
ORDER BY us.created_at DESC;

-- Add configuration for session management
CREATE TABLE IF NOT EXISTS session_config (
    id SERIAL PRIMARY KEY,
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default session configuration
INSERT INTO session_config (setting_name, setting_value, description) VALUES
('default_session_duration_hours', '24', 'Default session duration in hours'),
('max_sessions_per_user', '5', 'Maximum concurrent sessions per user'),
('cleanup_interval_minutes', '60', 'How often to run session cleanup in minutes'),
('session_extension_hours', '2', 'Hours to extend session on activity')
ON CONFLICT (setting_name) DO NOTHING;

-- Function to get session configuration
CREATE OR REPLACE FUNCTION get_session_config(config_name VARCHAR)
RETURNS TEXT AS $$
DECLARE
    config_value TEXT;
BEGIN
    SELECT setting_value INTO config_value
    FROM session_config
    WHERE setting_name = config_name;
    
    RETURN config_value;
END;
$$ LANGUAGE plpgsql;

-- Add session activity tracking (optional, for analytics)
CREATE TABLE IF NOT EXISTS session_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_token VARCHAR(255) NOT NULL,
    activity_type VARCHAR(50) NOT NULL, -- 'login', 'page_view', 'api_call', 'logout'
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_session_activity_token ON session_activity(session_token);
CREATE INDEX IF NOT EXISTS idx_session_activity_created_at ON session_activity(created_at);

-- Function to log session activity
CREATE OR REPLACE FUNCTION log_session_activity(
    token VARCHAR(255),
    activity VARCHAR(50),
    ip INET DEFAULT NULL,
    agent TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO session_activity (session_token, activity_type, ip_address, user_agent)
    VALUES (token, activity, ip, agent);
END;
$$ LANGUAGE plpgsql;

-- Update migration log
INSERT INTO migration_log (migration_name, description) VALUES
('001_optimize_postgresql_sessions', 'Added PostgreSQL session management optimizations, cleanup functions, and monitoring')
ON CONFLICT (migration_name) DO NOTHING;
