import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'

/**
 * GET /api/user/benefit-verifications
 * Get current user's benefit verification submissions
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's benefit verifications with company and benefit details
    const result = await query(
      `SELECT
        bv.id,
        bv.status,
        bv.comment,
        bv.created_at,
        c.name as company_name,
        c.id as company_id,
        b.name as benefit_name,
        b.id as benefit_id,
        cb.id as company_benefit_id
       FROM benefit_verifications bv
       JOIN company_benefits cb ON bv.company_benefit_id = cb.id
       JOIN companies c ON cb.company_id = c.id
       JOIN benefits b ON cb.benefit_id = b.id
       WHERE bv.user_id = $1::text
       ORDER BY bv.created_at DESC`,
      [user.id]
    )

    return NextResponse.json({
      verifications: result.rows
    })

  } catch (error) {
    console.error('Error retrieving user benefit verifications:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve benefit verifications' },
      { status: 500 }
    )
  }
}
