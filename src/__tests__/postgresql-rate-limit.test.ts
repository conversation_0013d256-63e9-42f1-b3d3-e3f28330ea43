// Tests for PostgreSQL rate limiting system

import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  checkRateLimit,
  checkSlidingWindowRateLimit,
  checkAPIRateLimit,
  checkAuthRateLimit,
  cleanupExpiredRateLimits,
  getRateLimitStats,
  resetRateLimit
} from '../lib/postgresql-rate-limit'

// Mock the database
vi.mock('../lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('../lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('PostgreSQL Rate Limiting', () => {
  beforeEach(() => {
    mockQuery.mockReset()
  })

  describe('Basic Rate Limiting', () => {
    it('should allow request when under limit', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{
          request_count: 1,
          request_timestamps: JSON.stringify([Date.now()])
        }]
      })

      const config = { windowMs: 60000, maxRequests: 100 }
      const result = await checkRateLimit('test_user', config)

      expect(result.allowed).toBe(true)
      expect(result.remaining).toBeGreaterThan(0)
      expect(result.total).toBe(100)
    })

    it('should deny request when over limit', async () => {
      const now = Date.now()
      const windowStart = Math.floor(now / 60000) * 60000
      // Create 100 timestamps within the current window (all recent)
      const timestamps = Array.from({ length: 100 }, (_, i) => windowStart + i * 100)

      mockQuery.mockResolvedValueOnce({
        rows: [{
          request_count: 101, // 100 existing + 1 new request
          request_timestamps: JSON.stringify([...timestamps, now])
        }]
      })

      const config = { windowMs: 60000, maxRequests: 100 }
      const result = await checkRateLimit('test_user', config)

      expect(result.allowed).toBe(false)
      expect(result.remaining).toBe(0)
    })

    it('should handle database errors gracefully', async () => {
      mockQuery.mockRejectedValueOnce(new Error('Database error'))

      const config = { windowMs: 60000, maxRequests: 100 }
      const result = await checkRateLimit('test_user', config)

      // Should fail open (allow request on error)
      expect(result.allowed).toBe(true)
    })
  })

  describe('Sliding Window Rate Limiting', () => {
    it('should work with sliding window', async () => {
      const now = Date.now()
      const timestamps = [now - 30000, now - 20000, now - 10000] // 3 requests in last minute
      
      mockQuery
        .mockResolvedValueOnce({
          rows: [{
            request_timestamps: JSON.stringify(timestamps)
          }]
        })
        .mockResolvedValueOnce({ rows: [{}] }) // Insert new request

      const config = { windowMs: 60000, maxRequests: 100 }
      const result = await checkSlidingWindowRateLimit('test_user', config)

      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(96) // 100 - 4 (3 existing + 1 new)
    })
  })

  describe('Convenience Functions', () => {
    it('should check API rate limit', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{
          request_count: 1,
          request_timestamps: JSON.stringify([Date.now()])
        }]
      })

      const result = await checkAPIRateLimit('test_user')
      expect(result.allowed).toBe(true)
    })

    it('should check auth rate limit', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{
          request_count: 1,
          request_timestamps: JSON.stringify([Date.now()])
        }]
      })

      const result = await checkAuthRateLimit('test_user')
      expect(result.allowed).toBe(true)
    })
  })

  describe('Maintenance Functions', () => {
    it('should cleanup expired rate limits', async () => {
      mockQuery.mockResolvedValueOnce({ rowCount: 5 })

      const result = await cleanupExpiredRateLimits()
      expect(result).toBe(5)
      expect(mockQuery).toHaveBeenCalledWith('DELETE FROM rate_limits WHERE expires_at < NOW()')
    })

    it('should get rate limit stats', async () => {
      const statsData = [{
        identifier: 'user1',
        total_windows: 3,
        total_requests: 15,
        last_request: new Date(),
        avg_requests_per_window: 5
      }]
      mockQuery.mockResolvedValueOnce({ rows: statsData })

      const result = await getRateLimitStats()
      expect(result).toEqual(statsData)
    })

    it('should reset rate limit for user', async () => {
      mockQuery.mockResolvedValueOnce({ rowCount: 2 })

      const result = await resetRateLimit('test_user')
      expect(result).toBe(true)
      expect(mockQuery).toHaveBeenCalledWith('DELETE FROM rate_limits WHERE identifier = $1', ['test_user'])
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty timestamps array', async () => {
      const now = Date.now()
      mockQuery.mockResolvedValueOnce({
        rows: [{
          request_count: 1, // The function adds the current request
          request_timestamps: JSON.stringify([now]) // Current request is added
        }]
      })

      const config = { windowMs: 60000, maxRequests: 100 }
      const result = await checkRateLimit('test_user', config)

      expect(result.allowed).toBe(true)
      expect(result.remaining).toBe(99) // 100 - 1 (new request)
    })

    it('should handle malformed timestamps', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{
          request_count: 1,
          request_timestamps: 'invalid json'
        }]
      })

      const config = { windowMs: 60000, maxRequests: 100 }
      const result = await checkRateLimit('test_user', config)

      // Should handle gracefully and allow request
      expect(result.allowed).toBe(true)
    })

    it('should filter out old timestamps', async () => {
      const now = Date.now()
      const windowStart = Math.floor(now / 60000) * 60000
      const oldTimestamp = windowStart - 120000 // Outside current window
      const recentTimestamp = windowStart + 30000 // Inside current window

      mockQuery.mockResolvedValueOnce({
        rows: [{
          request_count: 3, // 2 existing + 1 new request
          request_timestamps: JSON.stringify([oldTimestamp, recentTimestamp, now])
        }]
      })

      const config = { windowMs: 60000, maxRequests: 100 }
      const result = await checkRateLimit('test_user', config)

      expect(result.allowed).toBe(true)
      // Should only count the recent timestamp and new request, not the old one
      expect(result.remaining).toBe(98) // 100 - 2 (1 recent + 1 new)
    })
  })
})
