import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// GET /api/admin/missing-company-reports - Get all missing company reports
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status') // 'pending', 'reviewed', 'added', 'rejected'
    const domain = searchParams.get('domain')

    const offset = (page - 1) * limit

    const whereConditions: string[] = []
    const params: (string | number)[] = []
    let paramIndex = 1

    if (status) {
      whereConditions.push(`mcr.status = $${paramIndex}`)
      params.push(status)
      paramIndex++
    }

    if (domain) {
      whereConditions.push(`mcr.email_domain ILIKE $${paramIndex}`)
      params.push(`%${domain}%`)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // Get reports with company information if linked
    const reportsQuery = `
      SELECT 
        mcr.id,
        mcr.user_email,
        mcr.email_domain,
        mcr.first_name,
        mcr.last_name,
        mcr.status,
        mcr.admin_notes,
        mcr.created_at,
        mcr.updated_at,
        c.id as company_id,
        c.name as company_name
      FROM missing_company_reports mcr
      LEFT JOIN companies c ON mcr.company_id = c.id
      ${whereClause}
      ORDER BY mcr.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    params.push(limit, offset)

    const reportsResult = await query(reportsQuery, params)

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM missing_company_reports mcr
      ${whereClause}
    `

    const countParams = params.slice(0, -2) // Remove limit and offset
    const countResult = await query(countQuery, countParams)
    const total = parseInt(countResult.rows[0].total)

    // Get summary statistics
    const statsResult = await query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM missing_company_reports
      GROUP BY status
      ORDER BY status
    `)

    const stats = statsResult.rows.reduce((acc, row) => {
      acc[row.status] = parseInt(row.count)
      return acc
    }, {})

    return NextResponse.json({
      reports: reportsResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      stats
    })

  } catch (error) {
    console.error('Error fetching missing company reports:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch reports' },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/missing-company-reports - Update report status
export async function PATCH(request: NextRequest) {
  try {
    await requireAdmin()

    const body = await request.json()
    const { reportId, status, adminNotes } = body

    if (!reportId || !status) {
      return NextResponse.json(
        { error: 'Report ID and status are required' },
        { status: 400 }
      )
    }

    if (!['pending', 'reviewed', 'added', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be: pending, reviewed, added, or rejected' },
        { status: 400 }
      )
    }

    // Check if report exists
    const reportResult = await query(
      'SELECT id, user_email, email_domain FROM missing_company_reports WHERE id = $1',
      [reportId]
    )

    if (reportResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      )
    }

    // Update the report
    const updateResult = await query(
      'UPDATE missing_company_reports SET status = $1, admin_notes = $2, updated_at = NOW() WHERE id = $3 RETURNING *',
      [status, adminNotes || null, reportId]
    )

    const updatedReport = updateResult.rows[0]

    console.log(`✅ Updated missing company report ${reportId} to status: ${status}`)

    return NextResponse.json({
      success: true,
      message: `Report status updated to ${status}`,
      report: updatedReport
    })

  } catch (error) {
    console.error('Error updating missing company report:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update report' },
      { status: 500 }
    )
  }
}
