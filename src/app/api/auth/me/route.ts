import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/local-auth'

export async function GET(_request: NextRequest) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // Extract company domain from email
    const emailDomain = user.email.split('@')[1]

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        paymentStatus: user.paymentStatus,
        company_domain: emailDomain,
        is_premium: user.paymentStatus === 'premium'
      }
    })
  } catch (error) {
    console.error('Get user error:', error)
    return NextResponse.json(
      { error: 'Failed to get user' },
      { status: 500 }
    )
  }
}
