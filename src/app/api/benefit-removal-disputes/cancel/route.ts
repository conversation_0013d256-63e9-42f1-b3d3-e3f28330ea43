import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logBenefitRemovalDisputeCancelled } from '@/lib/activity-logger'

// POST /api/benefit-removal-disputes/cancel - Cancel a dispute
export async function POST(request: NextRequest) {
  try {
    const userId = await requireAuth()
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { disputeId } = body

    if (!disputeId) {
      return NextResponse.json(
        { error: 'Dispute ID is required' },
        { status: 400 }
      )
    }

    // Get the dispute details with benefit and company information
    const disputeResult = await query(`
      SELECT 
        brd.*,
        cb.is_verified,
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        b.id as benefit_id,
        b.name as benefit_name
      FROM benefit_removal_disputes brd
      JOIN company_benefits cb ON brd.company_benefit_id = cb.id
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE brd.id = $1
    `, [disputeId])

    if (disputeResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Dispute not found' },
        { status: 404 }
      )
    }

    const dispute = disputeResult.rows[0]

    // Check authorization - user can only cancel their own disputes
    if (dispute.user_id !== userId) {
      return NextResponse.json(
        { error: 'You can only cancel your own disputes' },
        { status: 403 }
      )
    }

    // Check domain authorization - user must still be from the same company
    const userDomain = user.email.split('@')[1]
    if (userDomain !== dispute.company_domain) {
      return NextResponse.json(
        { error: 'You can only cancel disputes for your own company' },
        { status: 403 }
      )
    }

    // Check if dispute can be cancelled (only pending disputes can be cancelled)
    if (dispute.status !== 'pending') {
      return NextResponse.json(
        { error: `Cannot cancel dispute with status: ${dispute.status}. Only pending disputes can be cancelled.` },
        { status: 400 }
      )
    }

    // Update the dispute status to cancelled
    const updateResult = await query(`
      UPDATE benefit_removal_disputes 
      SET status = 'cancelled', updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `, [disputeId])

    const cancelledDispute = updateResult.rows[0]

    // Log the activity
    await logBenefitRemovalDisputeCancelled(
      dispute.benefit_id,
      dispute.benefit_name,
      dispute.company_id,
      dispute.company_name,
      userId,
      user.email,
      user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : undefined,
      dispute.reason
    )

    return NextResponse.json({
      message: 'Dispute cancelled successfully',
      dispute: cancelledDispute
    })

  } catch (error) {
    console.error('Error cancelling benefit removal dispute:', error)
    return NextResponse.json(
      { error: 'Failed to cancel dispute' },
      { status: 500 }
    )
  }
}
