import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { getAllActivities } from '@/lib/activity-logger'

// GET /api/admin/activities/all - Get all activities with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const eventType = searchParams.get('eventType')
    const search = searchParams.get('search')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    
    // Validate pagination parameters
    if (page < 1) {
      return NextResponse.json(
        { error: 'Page must be greater than 0' },
        { status: 400 }
      )
    }
    
    if (limit < 1 || limit > 200) {
      return NextResponse.json(
        { error: 'Limit must be between 1 and 200' },
        { status: 400 }
      )
    }
    
    // Validate date parameters
    if (startDate && isNaN(Date.parse(startDate))) {
      return NextResponse.json(
        { error: 'Invalid start date format' },
        { status: 400 }
      )
    }
    
    if (endDate && isNaN(Date.parse(endDate))) {
      return NextResponse.json(
        { error: 'Invalid end date format' },
        { status: 400 }
      )
    }
    
    const result = await getAllActivities({
      page,
      limit,
      eventType: eventType || undefined,
      search: search || undefined,
      startDate: startDate || undefined,
      endDate: endDate || undefined
    })
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error('Error fetching all activities:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch activities' },
      { status: 500 }
    )
  }
}
