import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'

// GET /api/saved-companies/[companyId] - Check if company is saved by current user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json({ saved: false })
    }

    const { companyId } = await params
    const result = await query(
      'SELECT id FROM saved_companies WHERE user_id = $1 AND company_id = $2',
      [user.id, companyId]
    )

    return NextResponse.json({ saved: result.rows.length > 0 })
  } catch (error) {
    console.error('Error checking saved company:', error)
    return NextResponse.json({ saved: false })
  }
}
