-- Fix Materialized Views with Correct Column Names
-- This migration fixes the materialized views to use the actual column names from the database

-- Drop existing views if they exist
DROP MATERIALIZED VIEW IF EXISTS companies_with_benefits_cache CASCADE;
DROP MATERIALIZED VIEW IF EXISTS benefits_with_categories_cache CASCADE;

-- Companies with benefits materialized view (corrected)
CREATE MATERIALIZED VIEW companies_with_benefits_cache AS
SELECT 
    c.id,
    c.name,
    c.description,
    c.website,
    c.logo_url,
    c.size,
    c.industry,
    c.founded_year,
    c.verification_status,
    c.created_at,
    c.updated_at,
    COALESCE(
        json_agg(
            DISTINCT jsonb_build_object(
                'id', b.id,
                'name', b.name,
                'category_id', b.category_id,
                'icon', b.icon,
                'description', b.description,
                'category_name', bc.name,
                'category_display_name', bc.display_name,
                'category_icon', bc.icon
            )
        ) FILTER (WHERE b.id IS NOT NULL),
        '[]'::json
    ) as benefits,
    COALESCE(
        json_agg(
            DISTINCT jsonb_build_object(
                'id', cl.id,
                'location_type', cl.location_type,
                'city', cl.city,
                'country', cl.country,
                'country_code', cl.country_code,
                'is_primary', cl.is_primary,
                'is_headquarters', cl.is_headquarters,
                'location_raw', cl.location_raw,
                'location_normalized', cl.location_normalized
            )
        ) FILTER (WHERE cl.id IS NOT NULL),
        '[]'::json
    ) as locations
FROM companies c
LEFT JOIN company_benefits cb ON c.id = cb.company_id
LEFT JOIN benefits b ON cb.benefit_id = b.id
LEFT JOIN benefit_categories bc ON b.category_id = bc.id
LEFT JOIN company_locations cl ON c.id = cl.company_id
WHERE c.verification_status = 'verified'
GROUP BY c.id, c.name, c.description, c.website, c.logo_url, c.size, 
         c.industry, c.founded_year, c.verification_status, c.created_at, c.updated_at;

-- Index on materialized view
CREATE UNIQUE INDEX idx_companies_with_benefits_cache_id 
ON companies_with_benefits_cache(id);

CREATE INDEX idx_companies_with_benefits_cache_name 
ON companies_with_benefits_cache USING gin(to_tsvector('english', name));

CREATE INDEX idx_companies_with_benefits_cache_verification_status 
ON companies_with_benefits_cache(verification_status);

-- Benefits with categories cache
CREATE MATERIALIZED VIEW benefits_with_categories_cache AS
SELECT 
    b.id,
    b.name,
    b.category_id,
    b.icon,
    b.description,
    b.created_at,
    bc.name as category_name,
    bc.display_name as category_display_name,
    bc.icon as category_icon,
    bc.description as category_description,
    COUNT(cb.company_id) as company_count
FROM benefits b
LEFT JOIN benefit_categories bc ON b.category_id = bc.id
LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
LEFT JOIN companies c ON cb.company_id = c.id AND c.verification_status = 'verified'
GROUP BY b.id, b.name, b.category_id, b.icon, b.description, b.created_at,
         bc.name, bc.display_name, bc.icon, bc.description
ORDER BY b.name;

CREATE UNIQUE INDEX idx_benefits_with_categories_cache_id 
ON benefits_with_categories_cache(id);

CREATE INDEX idx_benefits_with_categories_cache_category_id 
ON benefits_with_categories_cache(category_id);

-- Update the refresh function to handle the new views
CREATE OR REPLACE FUNCTION refresh_cache_views()
RETURNS VOID AS $$
BEGIN
    -- Refresh materialized views
    REFRESH MATERIALIZED VIEW CONCURRENTLY companies_with_benefits_cache;
    REFRESH MATERIALIZED VIEW CONCURRENTLY benefits_with_categories_cache;
    
    -- Log the refresh
    INSERT INTO activity_log (
        event_type,
        event_description,
        created_at
    ) VALUES (
        'cache_refresh',
        'Materialized views refreshed',
        NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- Update migration log
INSERT INTO migration_log (migration_name, description) VALUES
('005_fix_materialized_views', 'Fixed materialized views with correct column names matching actual database schema')
ON CONFLICT (migration_name) DO NOTHING;
