'use client'

import { useState, useRef, useEffect } from 'react'
import { ChevronDown, X, Check } from 'lucide-react'

interface Option {
  value: string
  label: string
  count?: number
}

interface SearchableMultiSelectProps {
  options: Option[]
  selectedValues: string[]
  onSelectionChange: (values: string[]) => void
  placeholder: string
  icon?: React.ReactNode
  loading?: boolean
  maxDisplayed?: number
}

export function SearchableMultiSelect({
  options,
  selectedValues,
  onSelectionChange,
  placeholder,
  icon,
  loading = false,
  maxDisplayed = 3
}: SearchableMultiSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleToggleOption = (value: string) => {
    const newSelection = selectedValues.includes(value)
      ? selectedValues.filter(v => v !== value)
      : [...selectedValues, value]
    
    onSelectionChange(newSelection)
  }

  const handleRemoveOption = (value: string, event: React.MouseEvent) => {
    event.stopPropagation()
    onSelectionChange(selectedValues.filter(v => v !== value))
  }

  const getDisplayText = () => {
    if (selectedValues.length === 0) {return placeholder}
    
    const selectedOptions = options.filter(opt => selectedValues.includes(opt.value))
    
    if (selectedValues.length <= maxDisplayed) {
      return selectedOptions.map(opt => opt.label).join(', ')
    }
    
    const displayed = selectedOptions.slice(0, maxDisplayed).map(opt => opt.label).join(', ')
    const remaining = selectedValues.length - maxDisplayed
    return `${displayed} +${remaining} more`
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        onClick={() => setIsOpen(!isOpen)}
        className="w-full pl-8 sm:pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white cursor-pointer min-h-[40px] flex items-center"
      >
        {icon && (
          <div className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
            {icon}
          </div>
        )}

        <div className="flex-1 flex flex-wrap gap-1 min-w-0">
          {selectedValues.length > 0 && selectedValues.length <= maxDisplayed ? (
            selectedValues.map(value => {
              const option = options.find(opt => opt.value === value)
              return option ? (
                <span
                  key={value}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full max-w-full"
                >
                  <span className="truncate">{option.label}</span>
                  <button
                    onClick={(e) => handleRemoveOption(value, e)}
                    className="hover:bg-blue-200 rounded-full p-0.5 flex-shrink-0"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ) : null
            })
          ) : (
            <span className={`truncate ${selectedValues.length > 0 ? 'text-gray-900' : 'text-gray-500'} text-sm sm:text-base`}>
              {getDisplayText()}
            </span>
          )}
        </div>

        <ChevronDown className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-[50vh] sm:max-h-72 overflow-hidden">
          <div className="p-2 border-b border-gray-200 flex-shrink-0">
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded text-sm text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              autoFocus
            />
          </div>

          <div
            className="overflow-y-auto overscroll-contain webkit-overflow-scrolling-touch"
            style={{
              maxHeight: 'calc(50vh - 4rem)',
              WebkitOverflowScrolling: 'touch',
              scrollbarWidth: 'thin'
            }}
          >
            {loading ? (
              <div className="p-3 text-center text-gray-500 text-sm">Loading...</div>
            ) : filteredOptions.length > 0 ? (
              filteredOptions.map(option => (
                <div
                  key={option.value}
                  onClick={() => handleToggleOption(option.value)}
                  className="flex items-center justify-between px-3 py-3 sm:py-2 hover:bg-gray-50 cursor-pointer active:bg-gray-100 transition-colors touch-manipulation"
                  style={{ touchAction: 'manipulation' }}
                >
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div className={`w-4 h-4 border rounded flex items-center justify-center flex-shrink-0 ${
                      selectedValues.includes(option.value)
                        ? 'bg-blue-600 border-blue-600'
                        : 'border-gray-300'
                    }`}>
                      {selectedValues.includes(option.value) && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                    <span className="text-sm text-gray-900 truncate">{option.label}</span>
                  </div>
                  {option.count !== undefined && (
                    <span className="text-xs text-gray-500 flex-shrink-0 ml-2">({option.count})</span>
                  )}
                </div>
              ))
            ) : (
              <div className="p-3 text-center text-gray-500 text-sm">No options found</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
