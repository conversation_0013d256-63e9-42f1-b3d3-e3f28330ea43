# Multi-Location System Migration Guide

This document outlines the migration process for implementing the multi-location system in BenefitLens.

## Overview

The multi-location system allows companies to have multiple office locations while maintaining backward compatibility with the existing single location field.

## Database Changes

### New Tables

1. **company_locations** - Stores multiple locations per company
2. **location_mappings** - Normalizes location names and handles multi-language variants

### Schema Updates

- Added new tables with proper indexes and RLS policies
- Maintained existing `companies.location` field for backward compatibility
- Added foreign key relationships and constraints

## Migration Process

### 1. Database Schema Migration

Run the database migration to create the new tables:

```sql
-- Apply the migration
\i database/migrations/018-add-multi-location-support.sql
```

### 2. Data Migration

The data migration converts existing single location data to the new multi-location format.

#### Dry Run (Recommended First)

```bash
# Run a dry run to see what would be migrated
node scripts/migrate-company-locations.js
```

#### Live Migration

```bash
# Perform the actual migration
node scripts/migrate-company-locations.js --live
```

### 3. Verification

After migration, verify the data:

```sql
-- Check migration results
SELECT 
  c.name,
  c.location as legacy_location,
  cl.location_raw,
  cl.location_normalized,
  cl.is_primary,
  cl.is_headquarters
FROM companies c
LEFT JOIN company_locations cl ON c.id = cl.company_id
WHERE c.location IS NOT NULL
ORDER BY c.name;

-- Check location mappings
SELECT input_location, normalized_location, city, country 
FROM location_mappings 
ORDER BY normalized_location;
```

## Location Normalization

The system includes built-in normalization for common locations:

### German Cities
- `München` → `Munich, Germany`
- `Köln` → `Cologne, Germany`
- `Berlin` → `Berlin, Germany`
- etc.

### International Cities
- `London` → `London, United Kingdom`
- `Paris` → `Paris, France`
- `Amsterdam` → `Amsterdam, Netherlands`
- etc.

### Normalization Rules

1. **Language Variants**: Different language names map to the same normalized location
2. **Country Addition**: Cities without countries get appropriate country suffixes
3. **Format Standardization**: All locations follow "City, Country" format
4. **German City Detection**: Automatic detection of German cities based on patterns

## API Changes

### New Endpoints

- `GET /api/companies/{id}/locations` - Get company locations
- `POST /api/companies/{id}/locations` - Add company location
- `PATCH /api/companies/{id}/locations` - Update company location
- `DELETE /api/companies/{id}/locations` - Remove company location
- `GET /api/locations/suggestions` - Get location suggestions for autocomplete

### Updated Endpoints

- Company search now searches across all locations
- Company details include location arrays
- Admin endpoints support multi-location management

## Frontend Changes

### New Components

1. **LocationInput** - Autocomplete location input with suggestions
2. **MultiLocationInput** - Manage multiple locations with types and flags

### Updated Components

1. **CompanyCard** - Shows primary location + count of additional locations
2. **CompanySearch** - Uses LocationInput for better location search
3. **AdminDashboard** - Multi-location management in company forms
4. **CompanyDetailPage** - Displays all locations with badges

## Backward Compatibility

The system maintains full backward compatibility:

1. **Legacy Field**: `companies.location` field is preserved
2. **API Compatibility**: Existing API responses include location data
3. **Search Compatibility**: Search works across both old and new location data
4. **Display Fallback**: UI falls back to legacy location if no new locations exist

## Rollback Plan

If rollback is needed:

1. **Data Preservation**: Original location data is preserved in `companies.location`
2. **Table Removal**: New tables can be dropped without affecting core functionality
3. **Code Rollback**: Frontend can be reverted to use legacy location field

```sql
-- Rollback script (if needed)
DROP TABLE IF EXISTS company_locations CASCADE;
DROP TABLE IF EXISTS location_mappings CASCADE;
```

## Testing

### Manual Testing

1. **Migration Testing**:
   - Run dry run migration
   - Verify location normalization
   - Check data integrity

2. **Frontend Testing**:
   - Test location autocomplete
   - Verify multi-location display
   - Test admin location management

3. **Search Testing**:
   - Search by various location formats
   - Test location filtering
   - Verify search across multiple locations

### Automated Testing

Run the test suite to ensure all functionality works:

```bash
npm test
```

## Performance Considerations

1. **Indexes**: Proper indexes on location fields for fast search
2. **Caching**: Location suggestions can be cached
3. **Geocoding**: Rate limiting for external geocoding services
4. **Database**: Efficient joins between companies and locations

## Monitoring

Monitor the following after deployment:

1. **Search Performance**: Location-based search response times
2. **API Usage**: Location suggestion API usage
3. **Data Quality**: Location normalization accuracy
4. **User Adoption**: Usage of multi-location features

## Support

For issues or questions:

1. Check the migration logs for errors
2. Verify database constraints and indexes
3. Test location normalization with sample data
4. Review API endpoint responses

## Future Enhancements

Potential future improvements:

1. **Geocoding Integration**: Add coordinates for mapping
2. **Location Hierarchy**: Support for regions/states
3. **Distance Search**: Search by distance from location
4. **Location Analytics**: Track popular locations
5. **Bulk Import**: CSV import for multiple locations
