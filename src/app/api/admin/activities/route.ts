import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { getRecentActivities } from '@/lib/activity-logger'

// GET /api/admin/activities - Get recent activities for admin dashboard
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    
    // Validate limit
    if (limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Limit must be between 1 and 100' },
        { status: 400 }
      )
    }
    
    const activities = await getRecentActivities(limit)
    
    return NextResponse.json({
      activities,
      total: activities.length
    })
    
  } catch (error) {
    console.error('Error fetching admin activities:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch activities' },
      { status: 500 }
    )
  }
}
