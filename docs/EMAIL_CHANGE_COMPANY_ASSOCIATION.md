# Email Change Company Association

This document describes the automatic company association functionality that triggers when users change their email addresses in BenefitLens.

## Overview

BenefitLens uses email domain matching for automatic company benefit management permissions via magic link authentication. When a user updates their email address, the system automatically:

1. **Updates company association** if the new email domain matches a different company
2. **Removes company association** if the new email domain doesn't match any registered company
3. **Sends verification emails** to confirm new company associations
4. **Handles edge cases** like personal email domains and rate limiting

## How It Works

### Email Update Flow

1. User updates their email in `/api/auth/update-profile`
2. System detects email domain change
3. System calls `handleEmailChangeCompanyAssociation()` 
4. Based on the new domain, one of these actions occurs:
   - **No Change**: Domain unchanged or same company
   - **Company Updated**: New domain matches different company (requires verification)
   - **Company Removed**: New domain doesn't match any company
   - **Verification Required**: User must verify new company association

### Database Schema

#### Users Table
```sql
ALTER TABLE users ADD COLUMN company_id UUID REFERENCES companies(id) ON DELETE SET NULL;
```

#### Company Verification Tokens Table
```sql
CREATE TABLE company_verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_email VARCHAR(255) NOT NULL,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## API Endpoints

### Update Profile
`PATCH /api/auth/update-profile`

Enhanced to handle company association changes when email is updated.

**Response includes company association information:**
```json
{
  "user": { ... },
  "companyAssociation": {
    "action": "verification_required",
    "message": "Verification email sent for Company Name association...",
    "companyName": "Company Name",
    "verificationRequired": true,
    "verificationMessage": "Please check your email for a company verification link."
  }
}
```

### Verify Company Association
`POST /api/auth/verify-company`

Verifies company association tokens sent via email.

**Request:**
```json
{
  "token": "verification-token-uuid"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully associated with Company Name",
  "company": {
    "id": "company-uuid",
    "name": "Company Name"
  }
}
```

## Frontend Integration

### Verification Page
`/auth/verify-company?token=<token>`

Handles company verification flow with user-friendly interface showing:
- Loading state during verification
- Success state with company information
- Error state with retry options

## Edge Cases Handled

### 1. Personal Email Domains
- Gmail, Yahoo, Hotmail, etc. are detected as personal domains
- Company association is removed when switching to personal email
- No verification emails sent for personal domains

### 2. Rate Limiting
- Maximum 3 verification emails per hour per user
- Prevents spam and abuse
- Clear error messages when limit exceeded

### 3. Email Validation
- Validates email format before processing
- Rejects invalid email addresses
- Provides clear error messages

### 4. Domain Validation
- Checks for legitimate company domains
- Rejects known disposable email domains
- Validates domain format

### 5. Token Security
- Verification tokens expire after 24 hours
- Tokens are single-use only
- Old tokens are cleaned up when new ones are created

## Configuration

### Personal Email Domains
Add domains to the `PERSONAL_EMAIL_DOMAINS` set in `email-change-company-association.ts`:

```typescript
const PERSONAL_EMAIL_DOMAINS = new Set([
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com',
  // Add more as needed
])
```

### Rate Limiting
Adjust rate limiting constants:

```typescript
const VERIFICATION_EMAIL_RATE_LIMIT = 3 // emails per window
const VERIFICATION_EMAIL_WINDOW_HOURS = 1 // window duration
```

## Migration

Run the database migration to add the new fields:

```bash
psql -d workwell -f database/migrations/005-add-company-id-to-users.sql
```

This migration:
1. Adds `company_id` column to users table
2. Creates `company_verification_tokens` table
3. Populates existing users' `company_id` based on email domain matching
4. Adds necessary indexes

## Testing

Run the test suite:

```bash
npm test src/lib/__tests__/email-change-company-association.test.ts
```

Tests cover:
- Email domain change detection
- Company association updates
- Verification email sending
- Rate limiting
- Edge cases and error handling

## Security Considerations

1. **Email Verification**: All company associations require email verification
2. **Rate Limiting**: Prevents abuse of verification email system
3. **Token Expiration**: Verification tokens expire after 24 hours
4. **Domain Validation**: Prevents association with invalid domains
5. **Personal Email Detection**: Automatically handles personal email domains

## Future Enhancements

1. **Multiple Domains per Company**: Support companies with multiple email domains
2. **Domain Reputation Checking**: Integrate with domain reputation services
3. **Admin Override**: Allow admins to manually associate users with companies
4. **Audit Logging**: Track all company association changes
5. **Bulk Operations**: Handle bulk email updates efficiently
