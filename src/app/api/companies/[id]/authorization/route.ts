import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    
    // Check if user is authenticated
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({
        authorized: false,
        message: 'You must be signed in to verify benefits',
        requiresAuth: true
      })
    }

    // Get company information
    const companyResult = await query(
      'SELECT name, domain FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      return NextResponse.json({
        authorized: false,
        message: 'Company not found'
      })
    }

    const company = companyResult.rows[0]

    // Extract domain from user email
    const userDomain = user.email.split('@')[1]

    if (userDomain !== company.domain) {
      return NextResponse.json({
        authorized: false,
        message: `Employee verification required for ${company.name}`,
        companyName: company.name,
        requiredDomain: company.domain,
        userDomain
      })
    }

    return NextResponse.json({
      authorized: true,
      message: 'You are authorized to verify benefits for this company',
      companyName: company.name
    })
  } catch (error) {
    console.error('Error checking company authorization:', error)
    return NextResponse.json(
      { authorized: false, message: 'Authorization check failed' },
      { status: 500 }
    )
  }
}
