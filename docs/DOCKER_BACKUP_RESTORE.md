# Docker Development with Backup Restoration

This guide explains how to use Docker for local development with the ability to restore from production database backups.

## Overview

The BenefitLens application now supports Docker containerization for local development with automatic backup restoration capabilities. This allows you to:

- Run the complete application stack in containers
- Restore production data from backups
- Develop with real data locally
- Maintain consistent development environments

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- Production database backup files in the `./backups/` directory

## Quick Start

### Start with Latest Backup

To start the application with the latest backup automatically restored:

```bash
npm run dev:container:start-with-backup
```

This command will:
1. Start all containers (PostgreSQL, app, Adminer, MailHog)
2. Wait for PostgreSQL to be ready
3. Automatically restore from the latest backup file

### Manual Backup Restoration

If you want to restore from a specific backup:

```bash
# Start containers first
npm run dev:container:start

# Restore from latest backup
npm run dev:container:restore

# Or restore from specific backup
./scripts/restore-backup.sh backup_20250822_132722.sql
```

## Available Commands

### Container Management
- `npm run dev:container:build` - Build the application container
- `npm run dev:container:start` - Start all containers
- `npm run dev:container:stop` - Stop all containers
- `npm run dev:container:reset` - Reset containers and volumes
- `npm run dev:container:logs` - View container logs
- `npm run dev:container:rebuild` - Rebuild containers from scratch

### Backup Operations
- `npm run dev:container:restore` - Restore from latest backup
- `npm run dev:container:start-with-backup` - Start containers and restore backup
- `./scripts/restore-backup.sh [backup_file]` - Restore from specific backup

## Backup File Requirements

Backup files should be:
- Located in the `./backups/` directory
- Named with pattern: `backup_YYYYMMDD_HHMMSS.sql`
- Complete PostgreSQL dumps created with `pg_dump`

Example backup filename: `backup_20250822_132722.sql`

## Services and Ports

When running in Docker, the following services are available:

- **BenefitLens App**: http://localhost:3000
- **Adminer (Database UI)**: http://localhost:8080
- **MailHog (Email Testing)**: http://localhost:8025
- **PostgreSQL**: localhost:5432

## Database Restoration Process

The restoration script performs these steps:

1. **Wait for PostgreSQL**: Ensures the database is ready
2. **Terminate Connections**: Safely disconnects existing connections
3. **Drop Database**: Removes the existing database
4. **Create Database**: Creates a fresh database
5. **Restore Data**: Imports the complete backup

## Troubleshooting

### Port Conflicts
If you get port conflicts, ensure no other services are running on:
- Port 3000 (application)
- Port 5432 (PostgreSQL)
- Port 8080 (Adminer)
- Port 8025 (MailHog)

### Backup File Issues
- Ensure backup files are valid PostgreSQL dumps
- Check file permissions are readable
- Verify the backup file path is correct

### Container Issues
```bash
# View logs for debugging
npm run dev:container:logs

# Reset everything if needed
npm run dev:container:reset
```

## Development Workflow

1. **Start Development Environment**:
   ```bash
   npm run dev:container:start-with-backup
   ```

2. **Develop and Test**: Make changes to your code, the container will reflect changes

3. **Access Services**:
   - Application: http://localhost:3000
   - Database UI: http://localhost:8080
   - Email Testing: http://localhost:8025

4. **Stop When Done**:
   ```bash
   npm run dev:container:stop
   ```

## Data Persistence

- Database data is persisted in Docker volumes
- Application code changes are reflected immediately
- To start fresh, use `npm run dev:container:reset`

## Production Backup Integration

To use production backups:

1. **Create Backup** (on production):
   ```bash
   pg_dump -h localhost -U username -d benefitlens > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Copy to Development**:
   ```bash
   scp production:/path/to/backup.sql ./backups/
   ```

3. **Restore Locally**:
   ```bash
   npm run dev:container:start-with-backup
   ```

This workflow ensures your local development environment has the same data as production, making debugging and testing more effective.
