import { NextRequest, NextResponse } from 'next/server'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoSearchTrends } from '@/lib/demo-analytics-generator'
import { getSearchTrends } from '@/lib/analytics-tracker'

// For demo purposes, we'll simulate search trends
// In production, you'd track actual search queries in a separate table

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d' // 7d, 30d, 90d
    const limit = parseInt(searchParams.get('limit') || '10')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    if (accessInfo.level === 'none') {
      return NextResponse.json(
        { error: 'Authentication required to access analytics' },
        { status: 401 }
      )
    }

    // If user is on demo mode, return demo data
    if (accessInfo.isDemoMode) {
      const demoData = generateDemoSearchTrends(period, limit)
      return NextResponse.json(demoData)
    }

    // Get real search trends data
    const searchTrendsData = await getSearchTrends(period, limit)
    return NextResponse.json(searchTrendsData)

  } catch (error) {
    console.error('Error fetching search trends:', error)
    return NextResponse.json(
      { error: 'Failed to fetch search trends' },
      { status: 500 }
    )
  }
}
