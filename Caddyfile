# Your site(s). <PERSON><PERSON><PERSON> automatically redirects HTTP -> HTTPS.
benefitlens.de, www.benefitlens.de {
  # Optional: force www -> apex
  @www host www.benefitlens.de
  redir @www https://benefitlens.de{uri}

  encode gzip
  reverse_proxy benefitlens-app:3000  # resolves via shared "edge" network

  header {
    Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    X-Content-Type-Options "nosniff"
    Referrer-Policy "strict-origin-when-cross-origin"
  }
}

# Optional: expose Adminer over a subdomain with simple auth
# adminer.benefitlens.de {
#   encode gzip
#   basicauth {
#     # generate with: caddy hash-password
#     raul JDJhJDEwJHhv...hashed...string...
#   }
#   reverse_proxy benefitlens-adminer:8080
# }
