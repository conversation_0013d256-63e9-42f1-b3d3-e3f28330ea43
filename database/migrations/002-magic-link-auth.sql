-- Migration to implement magic link authentication
-- This removes password-based auth and adds magic link tokens

-- Create magic link tokens table
CREATE TABLE magic_link_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    user_data JSONB, -- For storing user data during sign-up flow
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_magic_link_tokens_token ON magic_link_tokens(token);
CREATE INDEX idx_magic_link_tokens_email ON magic_link_tokens(email);
CREATE INDEX idx_magic_link_tokens_expires_at ON magic_link_tokens(expires_at);

-- Remove password_hash column from users table
ALTER TABLE users DROP COLUMN IF EXISTS password_hash;

-- Add rate limiting table for magic link requests
CREATE TABLE magic_link_rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(email)
);

-- Add index for rate limiting
CREATE INDEX idx_magic_link_rate_limits_email ON magic_link_rate_limits(email);
CREATE INDEX idx_magic_link_rate_limits_window_start ON magic_link_rate_limits(window_start);

-- Clean up expired tokens function
CREATE OR REPLACE FUNCTION cleanup_expired_magic_links()
RETURNS void AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up expired tokens (if using pg_cron extension)
-- SELECT cron.schedule('cleanup-magic-links', '*/15 * * * *', 'SELECT cleanup_expired_magic_links();');
