import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser, requireAuth } from '@/lib/auth'
import { sendEmail } from '@/lib/email'
import { query } from '@/lib/local-db'

interface AdminNotificationData {
  reportId: string
  userEmail: string
  emailDomain: string
  firstName?: string
  lastName?: string
}

function createAdminNotificationEmail(data: AdminNotificationData) {
  const { reportId, userEmail, emailDomain, firstName, lastName } = data
  const userName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || 'User'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Company Report - BenefitLens</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px; }
        .info-box { background: white; padding: 15px; border-radius: 6px; margin: 10px 0; border-left: 4px solid #2563eb; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏢 New Company Report</h1>
          <p>A user has reported a missing company on BenefitLens</p>
        </div>
        <div class="content">
          <div class="info-box">
            <h3>📋 Report Details</h3>
            <p><strong>Report ID:</strong> ${reportId}</p>
            <p><strong>User:</strong> ${userName}</p>
            <p><strong>Email:</strong> ${userEmail}</p>
            <p><strong>Company Domain:</strong> ${emailDomain}</p>
          </div>

          <div class="info-box">
            <h3>🎯 Next Steps</h3>
            <p>Please review this company report and consider:</p>
            <ul>
              <li>Adding the company to the database</li>
              <li>Verifying the company domain</li>
              <li>Setting up initial benefits data</li>
              <li>Contacting the user if needed</li>
            </ul>
          </div>
        </div>
        <div class="footer">
          <p>BenefitLens Admin Notification System</p>
        </div>
      </div>
    </body>
    </html>
  `

  return {
    to: process.env.ADMIN_EMAIL || '<EMAIL>',
    subject: `🏢 New Company Report: ${emailDomain}`,
    html
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAuth()
    const user = await getCurrentUser()
    
    if (!user?.email) {
      return NextResponse.json(
        { error: 'User email not found' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { userEmail, emailDomain, firstName, lastName } = body

    if (!userEmail || !emailDomain) {
      return NextResponse.json(
        { error: 'User email and email domain are required' },
        { status: 400 }
      )
    }

    // Verify the user email matches the authenticated user
    if (userEmail.toLowerCase() !== user.email.toLowerCase()) {
      return NextResponse.json(
        { error: 'Email mismatch' },
        { status: 400 }
      )
    }

    // Check if there's already a recent report from this user for the same email domain
    const recentReportResult = await query(
      'SELECT id FROM missing_company_reports WHERE user_email = $1 AND email_domain = $2 AND created_at > NOW() - INTERVAL \'24 hours\'',
      [userEmail.toLowerCase(), emailDomain.toLowerCase()]
    )

    if (recentReportResult.rows.length > 0) {
      return NextResponse.json(
        { error: 'You have already submitted a report for this company domain in the last 24 hours. Please wait before submitting another report.' },
        { status: 429 }
      )
    }

    // Store the missing company report
    const reportResult = await query(
      `INSERT INTO missing_company_reports (user_email, email_domain, first_name, last_name, status, created_at)
       VALUES ($1, $2, $3, $4, 'pending', NOW()) RETURNING id`,
      [userEmail.toLowerCase(), emailDomain.toLowerCase(), firstName || null, lastName || null]
    )

    const reportId = reportResult.rows[0].id

    // Send notification email to admin
    const adminEmailOptions = createAdminNotificationEmail({
      reportId,
      userEmail,
      emailDomain,
      firstName,
      lastName
    })

    await sendEmail(adminEmailOptions)

    console.log('✅ Missing company report submitted:', { reportId, userEmail, emailDomain })

    return NextResponse.json({
      success: true,
      message: 'Thank you! We have received your report and will review it shortly. You will be notified via email once your company is added.',
      reportId
    })

  } catch (error) {
    console.error('Error submitting missing company report:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to submit report' },
      { status: 500 }
    )
  }
}
