-- Migration: Add activity log table
-- This migration adds a table to track all system activities for the admin dashboard

-- Create activity log table
CREATE TABLE activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('company_added', 'user_registered', 'benefit_verified', 'benefit_disputed')),
    event_description TEXT NOT NULL,
    user_id VARCHAR(255), -- User ID from auth system (nullable for system events)
    user_email VARCHAR(255), -- User email for display purposes
    user_name VARCHAR(255), -- User name for display purposes
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL, -- Related company (nullable)
    company_name VARCHAR(255), -- Company name for display purposes
    benefit_id UUID REFERENCES benefits(id) ON DELETE SET NULL, -- Related benefit (nullable)
    benefit_name VARCHAR(255), -- Benefit name for display purposes
    metadata JSONB, -- Additional event-specific data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_activity_log_event_type ON activity_log(event_type);
CREATE INDEX idx_activity_log_created_at ON activity_log(created_at DESC); -- DESC for recent activities
CREATE INDEX idx_activity_log_user_id ON activity_log(user_id);
CREATE INDEX idx_activity_log_company_id ON activity_log(company_id);
CREATE INDEX idx_activity_log_benefit_id ON activity_log(benefit_id);

-- Add comment to table
COMMENT ON TABLE activity_log IS 'Tracks all system activities for admin dashboard and audit purposes';
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: company_added, user_registered, benefit_verified, benefit_disputed';
COMMENT ON COLUMN activity_log.event_description IS 'Human-readable description of the event for display in admin dashboard';
COMMENT ON COLUMN activity_log.metadata IS 'Additional event-specific data stored as JSON';

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('009-add-activity-log', 'Add activity log table for tracking system events');
