-- Migration: Fix benefit_verifications foreign key constraint
-- This migration adds proper foreign key constraint to benefit_verifications.user_id
-- Date: 2025-01-31

-- First, check if there are any orphaned records that would prevent the foreign key creation
-- Delete any benefit_verifications records that reference non-existent users
DELETE FROM benefit_verifications
WHERE user_id::uuid NOT IN (SELECT id FROM users);

-- Change user_id column type from VARCHAR(255) to UUID for consistency
ALTER TABLE benefit_verifications
ALTER COLUMN user_id TYPE UUID USING user_id::uuid;

-- Add foreign key constraint with CASCADE delete
-- This ensures that when a user is deleted, their benefit verifications are also deleted
ALTER TABLE benefit_verifications
ADD CONSTRAINT fk_benefit_verifications_user_id
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add comment for documentation
COMMENT ON CONSTRAINT fk_benefit_verifications_user_id ON benefit_verifications 
IS 'Foreign key constraint to ensure benefit verifications are deleted when user is deleted';

-- Add to migration log
INSERT INTO migration_log (migration_name, description, applied_at) 
VALUES (
  '006-fix-benefit-verifications-foreign-key', 
  'Added proper foreign key constraint to benefit_verifications.user_id with CASCADE delete',
  NOW()
) ON CONFLICT (migration_name) DO NOTHING;

-- Create migration_log table if it doesn't exist (in case previous migration didn't run)
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
