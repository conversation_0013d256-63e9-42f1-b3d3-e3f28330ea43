import { logger } from './logger'
import { getPoolStats } from './local-db'
import { checkCacheHealth } from './postgresql-cache'

export interface PerformanceMetrics {
  timestamp: string
  memory: {
    heapUsed: number
    heapTotal: number
    external: number
    rss: number
    heapUsedMB: number
    heapTotalMB: number
    rssMB: number
  }
  cpu: {
    userCPUTime: number
    systemCPUTime: number
  }
  uptime: number
  database: {
    totalConnections: number
    idleConnections: number
    waitingConnections: number
    maxConnections: number
  }
  cache: {
    isHealthy: boolean
  }
  eventLoop: {
    delay: number
  }
}

class PerformanceMonitor {
  private metricsInterval: NodeJS.Timeout | null = null
  private isMonitoring = false

  // Measure event loop delay
  private measureEventLoopDelay(): Promise<number> {
    return new Promise((resolve) => {
      const start = process.hrtime.bigint()
      setImmediate(() => {
        const delta = process.hrtime.bigint() - start
        const delayMs = Number(delta) / 1000000 // Convert nanoseconds to milliseconds
        resolve(delayMs)
      })
    })
  }

  async collectMetrics(): Promise<PerformanceMetrics> {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    const poolStats = getPoolStats()
    const eventLoopDelay = await this.measureEventLoopDelay()
    
    let cacheHealthy = false
    try {
      cacheHealthy = await checkCacheHealth()
    } catch (error) {
      // Cache health check failed, but we don't want to fail the entire metrics collection
      logger.debug('Cache health check failed during metrics collection', { error: error as Error })
    }

    return {
      timestamp: new Date().toISOString(),
      memory: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
        heapUsedMB: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(memUsage.heapTotal / 1024 / 1024),
        rssMB: Math.round(memUsage.rss / 1024 / 1024),
      },
      cpu: {
        userCPUTime: cpuUsage.user,
        systemCPUTime: cpuUsage.system,
      },
      uptime: process.uptime(),
      database: {
        totalConnections: poolStats.totalCount,
        idleConnections: poolStats.idleCount,
        waitingConnections: poolStats.waitingCount,
        maxConnections: poolStats.maxConnections || 0,
      },
      cache: {
        isHealthy: cacheHealthy,
      },
      eventLoop: {
        delay: eventLoopDelay,
      },
    }
  }

  async logMetrics(): Promise<void> {
    try {
      const metrics = await this.collectMetrics()
      
      // Log warnings for concerning metrics
      if (metrics.memory.heapUsedMB > 500) {
        logger.warn('High memory usage detected', {
          heapUsedMB: metrics.memory.heapUsedMB,
          heapTotalMB: metrics.memory.heapTotalMB,
        })
      }

      if (metrics.eventLoop.delay > 100) {
        logger.warn('High event loop delay detected', {
          delayMs: metrics.eventLoop.delay,
        })
      }

      if (metrics.database.waitingConnections > 5) {
        logger.warn('High database connection wait queue', {
          waitingConnections: metrics.database.waitingConnections,
          totalConnections: metrics.database.totalConnections,
        })
      }

      if (!metrics.cache.isHealthy) {
        logger.warn('Cache health check failed')
      }

      // Log performance metrics
      logger.performanceMetric('memory_heap_used', metrics.memory.heapUsedMB, 'MB')
      logger.performanceMetric('event_loop_delay', metrics.eventLoop.delay, 'ms')
      logger.performanceMetric('database_connections_total', metrics.database.totalConnections, 'count')
      logger.performanceMetric('database_connections_waiting', metrics.database.waitingConnections, 'count')
      
    } catch (error) {
      logger.error('Failed to collect performance metrics', { error: error as Error })
    }
  }

  startMonitoring(intervalMs: number = 60000): void {
    if (this.isMonitoring) {
      logger.warn('Performance monitoring is already running')
      return
    }

    logger.info('Starting performance monitoring', { intervalMs })
    this.isMonitoring = true
    
    // Log initial metrics
    this.logMetrics()
    
    // Set up periodic monitoring
    this.metricsInterval = setInterval(() => {
      this.logMetrics()
    }, intervalMs)
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    logger.info('Stopping performance monitoring')
    this.isMonitoring = false
    
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval)
      this.metricsInterval = null
    }
  }

  isRunning(): boolean {
    return this.isMonitoring
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor()

// Helper function to start monitoring in production
export function startPerformanceMonitoring(): void {
  if (process.env.NODE_ENV === 'production') {
    // Monitor every 5 minutes in production
    performanceMonitor.startMonitoring(5 * 60 * 1000)
  } else if (process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
    // Monitor every minute in development if explicitly enabled
    performanceMonitor.startMonitoring(60 * 1000)
  }
}

// Graceful shutdown - only register signal handlers once to prevent memory leaks
if (!process.env.PERFORMANCE_SIGNAL_HANDLERS_REGISTERED) {
  process.env.PERFORMANCE_SIGNAL_HANDLERS_REGISTERED = 'true'
  process.on('SIGINT', () => {
    performanceMonitor.stopMonitoring()
  })

  process.on('SIGTERM', () => {
    performanceMonitor.stopMonitoring()
  })
}

// Performance timing utility for measuring function execution
export class PerformanceTimer {
  private startTime: bigint
  private name: string

  constructor(name: string) {
    this.name = name
    this.startTime = process.hrtime.bigint()
  }

  end(context?: Record<string, unknown>): number {
    const endTime = process.hrtime.bigint()
    const durationNs = endTime - this.startTime
    const durationMs = Number(durationNs) / 1000000

    logger.performanceMetric(this.name, durationMs, 'ms', context)
    
    return durationMs
  }
}

// Decorator for timing async functions
export function timed(name?: string) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const timerName = name || `${target.constructor.name}.${propertyKey}`

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    descriptor.value = async function (...args: any[]) {
      const timer = new PerformanceTimer(timerName)
      try {
        const result = await originalMethod.apply(this, args)
        timer.end({ success: true })
        return result
      } catch (error) {
        timer.end({ success: false, error: (error as Error).message })
        throw error
      }
    }

    return descriptor
  }
}
