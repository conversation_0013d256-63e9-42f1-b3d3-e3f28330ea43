import { randomBytes, timingSafeEqual } from 'crypto'
import { cookies } from 'next/headers'
import { setCSRFToken as setCSRFTokenPG, getCSRFToken as getCSRFTokenPG } from './postgresql-session'
import { query } from './local-db'

const CSRF_TOKEN_LENGTH = 32
const CSRF_TOKEN_TTL = 3600 // 1 hour

export function generateCSRFToken(): string {
  return randomBytes(CSRF_TOKEN_LENGTH).toString('hex')
}

export async function setCSRFToken(sessionId: string): Promise<string> {
  const token = generateCSRFToken()

  // Store in PostgreSQL with TTL
  await setCSRFTokenPG(sessionId, token, CSRF_TOKEN_TTL)

  // Also set as httpOnly cookie
  const cookieStore = await cookies()
  cookieStore.set('csrf_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: CSRF_TOKEN_TTL,
  })

  return token
}

export async function getCSRFToken(sessionId: string): Promise<string | null> {
  return await getCSRFTokenPG(sessionId)
}

export async function validateCSRFToken(sessionId: string, providedToken: string): Promise<boolean> {
  if (!providedToken || !sessionId) {
    return false
  }

  const storedToken = await getCSRFToken(sessionId)

  if (!storedToken) {
    return false
  }

  // Constant-time comparison to prevent timing attacks
  return timingSafeEqual(Buffer.from(storedToken), Buffer.from(providedToken))
}

export async function deleteCSRFToken(sessionId: string): Promise<void> {
  // Delete from PostgreSQL
  await query('DELETE FROM csrf_tokens WHERE session_id = $1', [sessionId])

  // Clear cookie
  const cookieStore = await cookies()
  cookieStore.delete('csrf_token')
}



// Middleware helper to check CSRF for state-changing operations
export function requiresCSRFProtection(method: string, pathname: string): boolean {
  // Protect all non-GET requests to API routes (except auth routes)
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth/')) {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)
  }
  
  return false
}

// Extract CSRF token from request
export function extractCSRFToken(request: Request): string | null {
  // Check X-CSRF-Token header first
  const headerToken = request.headers.get('X-CSRF-Token')
  if (headerToken) {
    return headerToken
  }
  
  // For form submissions, check the body (this would need to be implemented in the API route)
  return null
}
