#!/usr/bin/env node

// <PERSON>ript to update .env.example to remove Redis references

import fs from 'fs'
import path from 'path'

const envExamplePath = path.join(process.cwd(), '.env.example')

try {
  if (!fs.existsSync(envExamplePath)) {
    console.log('⚠️  .env.example not found, skipping...')
    process.exit(0)
  }

  // Read .env.example
  let envContent = fs.readFileSync(envExamplePath, 'utf8')
  
  // Remove Redis-related environment variables
  const redisVars = [
    'REDIS_URL',
    'REDIS_HOST',
    'REDIS_PORT',
    'REDIS_PASSWORD',
    'REDIS_DB'
  ]
  
  let removedVars = []
  
  redisVars.forEach(varName => {
    const regex = new RegExp(`^${varName}=.*$`, 'gm')
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, '')
      removedVars.push(varName)
    }
  })
  
  // Remove Redis comments
  envContent = envContent.replace(/^#.*[Rr]edis.*$/gm, '')
  
  // Add PostgreSQL cache configuration
  if (!envContent.includes('CACHE_TYPE')) {
    envContent += '\n# Cache Configuration\n'
    envContent += 'CACHE_TYPE=postgresql\n'
    envContent += '# CACHE_DEFAULT_TTL=3600\n'
    envContent += '# CACHE_CLEANUP_INTERVAL=3600\n'
  }
  
  // Clean up extra newlines
  envContent = envContent.replace(/\n\n\n+/g, '\n\n')
  
  // Write updated .env.example
  fs.writeFileSync(envExamplePath, envContent)
  
  console.log('✅ .env.example updated successfully!')
  if (removedVars.length > 0) {
    console.log('🗑️  Removed Redis variables:', removedVars.join(', '))
  }
  console.log('➕ Added PostgreSQL cache configuration')
  
} catch (error) {
  console.error('❌ Error updating .env.example:', error.message)
  process.exit(1)
}
