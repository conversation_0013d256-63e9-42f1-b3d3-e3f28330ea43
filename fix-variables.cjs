#!/usr/bin/env node

const fs = require('fs');

function fixVariableReferences(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix all patterns where response is used but was declared as _response
    const patterns = [
      // Pattern 1: const _response = ... followed by response usage
      {
        search: /const _response = await fetch\(([^)]+)\)\s*\n\s*if \(response\.ok\)/g,
        replace: (match, fetchCall) => `const response = await fetch(${fetchCall})\n      if (response.ok)`
      },
      {
        search: /const _response = await fetch\(([^)]+)\)\s*\n\s*const data = await response\.json\(\)/g,
        replace: (match, fetchCall) => `const response = await fetch(${fetchCall})\n        const data = await response.json()`
      },
      {
        search: /const _response = await fetch\(([^)]+)\)\s*\n([^}]+)\n\s*if \(response\.ok\)/g,
        replace: (match, fetchCall, middleContent) => `const response = await fetch(${fetchCall})\n${middleContent}\n      if (response.ok)`
      },
      // Pattern 2: Missing response declaration
      {
        search: /(\s+)const response = await fetch\(([^)]+)\)\s*\n\s*}\)\s*\n\s*if \(response\.ok\)/g,
        replace: (match, indent, fetchCall) => `${indent}const response = await fetch(${fetchCall})\n      })\n\n      if (response.ok)`
      },
      // Pattern 3: Fix error variables
      {
        search: /const _error = await response\.json\(\)\s*\n\s*alert\(error\.error/g,
        replace: 'const error = await response.json()\n        alert(error.error'
      },
      {
        search: /const _error = await response\.json\(\)\s*\n\s*const errorMessage = error\.error/g,
        replace: 'const error = await response.json()\n        const errorMessage = error.error'
      }
    ];

    patterns.forEach(pattern => {
      const newContent = content.replace(pattern.search, pattern.replace);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });

    // Fix missing response declarations by finding fetch calls without proper response variable
    const fetchWithoutResponse = /(\s+)await fetch\(([^)]+)\)\s*\n\s*}\)\s*\n\s*if \(response\.ok\)/g;
    content = content.replace(fetchWithoutResponse, (match, indent, fetchCall) => {
      modified = true;
      return `${indent}const response = await fetch(${fetchCall})\n      })\n\n      if (response.ok)`;
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed variable references in: ${filePath}`);
      return true;
    }

    return false;

  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Fix the admin-dashboard file
const filePath = './src/components/admin-dashboard.tsx';
if (fs.existsSync(filePath)) {
  fixVariableReferences(filePath);
} else {
  console.log('File not found:', filePath);
}

console.log('Variable reference fixes applied!');
