'use client'

import { useState, useEffect } from 'react'

interface AuthorizationStatus {
  authorized: boolean
  message: string
  requiresAuth?: boolean
  companyName?: string
  requiredDomain?: string
  userDomain?: string
}

export function useCompanyAuthorization(companyId: string) {
  const [authStatus, setAuthStatus] = useState<AuthorizationStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkCompanyAuthorization = async () => {
      if (!companyId) {
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        const response = await fetch(`/api/companies/${companyId}/authorization`)
        if (response.ok) {
          const authResult = await response.json()
          setAuthStatus(authResult)
        } else {
          setAuthStatus({
            authorized: false,
            message: 'Unable to check authorization'
          })
        }
      } catch (error) {
        console.error('Error checking company authorization:', error)
        setAuthStatus({
          authorized: false,
          message: 'Authorization check failed'
        })
      } finally {
        setIsLoading(false)
      }
    }

    checkCompanyAuthorization()
  }, [companyId])

  return { authStatus, isLoading }
}
