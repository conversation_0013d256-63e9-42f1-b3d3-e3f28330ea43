'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  AlertTriangle as _AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface BenefitCategory {
  id: string
  name: string
  display_name: string
  description: string | null
  icon: string | null
  sort_order: number
  is_active: boolean
  benefit_count: number
  created_at: string
  updated_at: string
}

interface AdminBenefitCategoriesProps {
  onClose?: () => void
}

interface CategoryRowProps {
  category: BenefitCategory
  isEditing: boolean
  onEdit: (category: BenefitCategory) => void
  onSave: (category: BenefitCategory) => void
  onCancel: () => void
  onDelete: (category: BenefitCategory) => void
}

function CategoryRow({ category, isEditing, onEdit, onSave, onCancel, onDelete }: CategoryRowProps) {
  const [editData, setEditData] = useState(category)

  useEffect(() => {
    setEditData(category)
  }, [category])

  const handleSave = () => {
    onSave(editData)
  }

  if (isEditing) {
    return (
      <tr className="bg-blue-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="text"
            value={editData.name}
            onChange={(e) => setEditData({ ...editData, name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="text"
            value={editData.display_name}
            onChange={(e) => setEditData({ ...editData, display_name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </td>
        <td className="px-6 py-4">
          <textarea
            value={editData.description || ''}
            onChange={(e) => setEditData({ ...editData, description: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={2}
          />
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="text"
            value={editData.icon || ''}
            onChange={(e) => setEditData({ ...editData, icon: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="number"
            value={editData.sort_order}
            onChange={(e) => setEditData({ ...editData, sort_order: parseInt(e.target.value) || 0 })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            category.is_active
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {category.is_active ? 'Active' : 'Inactive'}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              className="text-green-600 hover:text-green-900"
            >
              <Save className="h-4 w-4" />
            </button>
            <button
              onClick={onCancel}
              className="text-gray-600 hover:text-gray-900"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    )
  }

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        {category.name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.display_name}
      </td>
      <td className="px-6 py-4 text-sm text-gray-900">
        {category.description}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.icon}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.sort_order}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          category.is_active
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {category.is_active ? 'Active' : 'Inactive'}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div className="flex space-x-2">
          <button
            onClick={() => onEdit(category)}
            className="text-blue-600 hover:text-blue-900"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={() => onDelete(category)}
            className="text-red-600 hover:text-red-900"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </td>
    </tr>
  )
}

export function AdminBenefitCategories({ onClose }: AdminBenefitCategoriesProps) {
  const [categories, setCategories] = useState<BenefitCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingCategory, setEditingCategory] = useState<BenefitCategory | null>(null)
  const [showInactive, setShowInactive] = useState(false)
  const [showMigrateModal, setShowMigrateModal] = useState(false)
  const [migratingCategory, setMigratingCategory] = useState<BenefitCategory | null>(null)
  const [targetCategoryId, setTargetCategoryId] = useState('')
  
  const [newCategory, setNewCategory] = useState({
    name: '',
    display_name: '',
    description: '',
    icon: '',
    sort_order: 0
  })

  const fetchCategories = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (showInactive) {
        params.append('includeInactive', 'true')
      }

      const response = await fetch(`/api/admin/benefit-categories?${params}`)
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      setLoading(false)
    }
  }, [showInactive])

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const handleAddCategory = async () => {
    try {
      const response = await fetch('/api/admin/benefit-categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCategory)
      })

      if (response.ok) {
        setShowAddModal(false)
        setNewCategory({
          name: '',
          display_name: '',
          description: '',
          icon: '',
          sort_order: 0
        })
        fetchCategories()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create category')
      }
    } catch (error) {
      console.error('Error creating category:', error)
      alert('Failed to create category')
    }
  }

  const handleUpdateCategory = async (category: BenefitCategory) => {
    try {
      const response = await fetch(`/api/admin/benefit-categories/${category.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: category.name,
          display_name: category.display_name,
          description: category.description,
          icon: category.icon,
          sort_order: category.sort_order,
          is_active: category.is_active
        })
      })

      if (response.ok) {
        setEditingCategory(null)
        fetchCategories()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to update category')
      }
    } catch (error) {
      console.error('Error updating category:', error)
      alert('Failed to update category')
    }
  }

  const handleDeleteCategory = async (category: BenefitCategory) => {
    if (category.benefit_count > 0) {
      if (confirm(`Category "${category.display_name}" has ${category.benefit_count} benefits. Would you like to migrate them to another category first?`)) {
        setMigratingCategory(category)
        setShowMigrateModal(true)
        return
      } else {
        return
      }
    }

    if (!confirm(`Delete category "${category.display_name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/benefit-categories/${category.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchCategories()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to delete category')
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('Failed to delete category')
    }
  }

  const handleMigrateBenefits = async () => {
    if (!migratingCategory || !targetCategoryId) {
      return
    }

    try {
      const response = await fetch(`/api/admin/benefit-categories/${migratingCategory.id}/migrate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ targetCategoryId })
      })

      if (response.ok) {
        const result = await response.json()
        alert(`${result.message}. You can now delete the category if desired.`)
        setShowMigrateModal(false)
        setMigratingCategory(null)
        setTargetCategoryId('')
        fetchCategories()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to migrate benefits')
      }
    } catch (error) {
      console.error('Error migrating benefits:', error)
      alert('Failed to migrate benefits')
    }
  }

  const generateNameFromDisplayName = (displayName: string) => {
    return displayName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .trim()
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Benefit Categories</h3>
          <p className="text-sm text-gray-600 mt-1">
            Manage benefit categories for organizing benefits.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowInactive(!showInactive)}
            className="flex items-center gap-2"
          >
            {showInactive ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            {showInactive ? 'Hide Inactive' : 'Show Inactive'}
          </Button>
          <Button
            size="sm"
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Category
          </Button>
          {onClose && (
            <Button variant="outline" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Loading categories...</p>
        </div>
      ) : categories.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Benefits
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {categories.map((category) => (
                <CategoryRow
                  key={category.id}
                  category={category}
                  isEditing={editingCategory?.id === category.id}
                  onEdit={setEditingCategory}
                  onSave={handleUpdateCategory}
                  onCancel={() => setEditingCategory(null)}
                  onDelete={handleDeleteCategory}
                />
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No categories found</p>
        </div>
      )}

      {/* Add Category Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Category</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Display Name
                </label>
                <input
                  type="text"
                  value={newCategory.display_name}
                  onChange={(e) => {
                    const displayName = e.target.value
                    setNewCategory({
                      ...newCategory,
                      display_name: displayName,
                      name: generateNameFromDisplayName(displayName)
                    })
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="e.g., Health & Medical"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Internal Name
                </label>
                <input
                  type="text"
                  value={newCategory.name}
                  onChange={(e) => setNewCategory({...newCategory, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="e.g., health_medical"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Lowercase letters, numbers, and underscores only
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={newCategory.description}
                  onChange={(e) => setNewCategory({...newCategory, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  rows={3}
                  placeholder="Brief description of this category"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Icon (emoji)
                </label>
                <input
                  type="text"
                  value={newCategory.icon}
                  onChange={(e) => setNewCategory({...newCategory, icon: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                  placeholder="🏥"
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddCategory}
                disabled={!newCategory.name || !newCategory.display_name}
              >
                Create Category
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Migrate Benefits Modal */}
      {showMigrateModal && migratingCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Migrate Benefits
            </h3>

            <p className="text-sm text-gray-600 mb-4">
              Category &quot;{migratingCategory.display_name}&quot; has {migratingCategory.benefit_count} benefits.
              Select a target category to migrate them to:
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Target Category
                </label>
                <select
                  value={targetCategoryId}
                  onChange={(e) => setTargetCategoryId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                >
                  <option value="">Select a category...</option>
                  {categories
                    .filter(cat => cat.id !== migratingCategory.id && cat.is_active)
                    .map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.icon && `${category.icon} `}{category.display_name}
                      </option>
                    ))}
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowMigrateModal(false)
                  setMigratingCategory(null)
                  setTargetCategoryId('')
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleMigrateBenefits}
                disabled={!targetCategoryId}
              >
                Migrate Benefits
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
