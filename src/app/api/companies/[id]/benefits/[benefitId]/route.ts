import { NextRequest, NextResponse } from 'next/server'
import { removeCompanyBenefit } from '@/lib/database'
import { getCurrentUser, canUserManageCompany } from '@/lib/auth'

/**
 * DELETE /api/companies/[id]/benefits/[benefitId]
 * Remove a specific benefit from a company
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; benefitId: string }> }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const { id: companyId, benefitId } = await params

    // Check if user can manage this company based on email domain
    const canManage = await canUserManageCompany(companyId)
    if (!canManage) {
      return NextResponse.json(
        { error: 'You can only manage benefits for companies that match your email domain' },
        { status: 403 }
      )
    }

    await removeCompanyBenefit(companyId, benefitId)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error removing company benefit:', error)
    return NextResponse.json(
      { error: 'Failed to remove company benefit' },
      { status: 500 }
    )
  }
}
