# Mailgun Setup Guide for BenefitLens

This guide walks you through setting up Mailgun for production email delivery.

## Step 1: Create Mailgun Account

1. Go to [mailgun.com](https://www.mailgun.com)
2. Sign up for a free account (5,000 emails/month permanently free)
3. Verify your email address

## Step 2: Add Your Domain

1. In the Mailgun dashboard, go to **Sending** → **Domains**
2. Click **Add New Domain**
3. Enter your domain: `benefitlens.de`
4. Choose **EU** region for GDPR compliance
5. Click **Add Domain**

## Step 3: Configure DNS Records

Mailgun will provide you with DNS records to add to your domain. Add these to your DNS provider:

### Required Records:
- **TXT record** for domain verification
- **TXT record** for SPF (Sender Policy Framework)
- **CNAME record** for DKIM (DomainKeys Identified Mail)
- **CNAME record** for tracking (optional)

### Example DNS Records:
```
Type: TXT
Name: benefitlens.de
Value: v=spf1 include:mailgun.org ~all

Type: TXT  
Name: _domainkey.benefitlens.de
Value: (provided by Mailgun)

Type: CNAME
Name: email.benefitlens.de
Value: mailgun.org
```

## Step 4: Get Your Credentials

Once DNS is verified (can take up to 48 hours):

1. Go to **Sending** → **Domain settings** → **SMTP credentials**
2. Note down:
   - **SMTP hostname**: `smtp.mailgun.org`
   - **Port**: `587` (TLS) or `465` (SSL)
   - **Username**: `<EMAIL>`
   - **Password**: Your Mailgun API key

## Step 5: Update Production Environment

Copy `.env.production.example` to `.env.production` and update:

```bash
# Email (Mailgun Configuration)
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_actual_mailgun_api_key
FROM_EMAIL=<EMAIL>
```

## Step 6: Test Email Delivery

### Development Testing (MailHog)
```bash
npm run test:email <EMAIL>
```
Check http://localhost:8025 to see the test email.

### Production Testing (Mailgun)
1. Update your production environment variables
2. Deploy your application
3. Run the test script:
   ```bash
   NODE_ENV=production npm run test:email <EMAIL>
   ```
4. Check your actual email inbox
5. Monitor Mailgun dashboard for delivery statistics

## Important Notes

- **Free Tier**: 5,000 emails/month permanently free
- **EU Compliance**: Choose EU region for GDPR compliance
- **Domain Verification**: Can take up to 48 hours
- **Deliverability**: Much better than self-hosted email
- **Monitoring**: Use Mailgun dashboard to track delivery

## Troubleshooting

### DNS Not Verifying
- Wait up to 48 hours for DNS propagation
- Use `dig` or online DNS checkers to verify records
- Contact your DNS provider if issues persist

### Emails Going to Spam
- Ensure all DNS records are properly configured
- Start with low volume to build sender reputation
- Monitor bounce rates and spam complaints

### Authentication Errors
- Double-check SMTP credentials
- Ensure API key has proper permissions
- Verify domain is fully verified in Mailgun

## Security Best Practices

- Keep your Mailgun API key secure
- Use environment variables, never commit credentials
- Monitor your Mailgun dashboard regularly
- Set up webhook notifications for bounces/complaints
