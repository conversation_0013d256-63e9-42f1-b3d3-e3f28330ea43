apiVersion: apps/v1
kind: Deployment
metadata:
  name: benefitlens
  namespace: benefitlens
spec:
  replicas: 3
  selector:
    matchLabels:
      app: benefitlens
  template:
    metadata:
      labels:
        app: benefitlens
    spec:
      containers:
      - name: benefitlens
        image: your-registry/benefitlens:latest  # Replace with your actual image
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: benefitlens-config
        - secretRef:
            name: benefitlens-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        fsGroup: 1001
---
apiVersion: v1
kind: Service
metadata:
  name: benefitlens
  namespace: benefitlens
spec:
  selector:
    app: benefitlens
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: benefitlens-ingress
  namespace: benefitlens
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"  # Optional: for SSL certificates
spec:
  tls:
  - hosts:
    - your-domain.com  # Replace with your actual domain
    secretName: benefitlens-tls
  rules:
  - host: your-domain.com  # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: benefitlens
            port:
              number: 80
