import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

/**
 * GET /api/admin/missing-companies
 * Get all missing company reports for admin review
 */
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status') // 'pending', 'reviewed', 'added', 'rejected'

    const offset = (page - 1) * limit

    const whereConditions: string[] = []
    const params: (string | number)[] = []
    let paramIndex = 1

    if (status) {
      whereConditions.push(`status = $${paramIndex}`)
      params.push(status)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : ''

    // Get missing company reports
    const result = await query(
      `SELECT 
        id,
        user_email,
        email_domain,
        first_name,
        last_name,
        status,
        admin_notes,
        company_id,
        created_at,
        updated_at
       FROM missing_company_reports
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      [...params, limit, offset]
    )

    // Get total count for pagination
    const countResult = await query(
      `SELECT COUNT(*) as total
       FROM missing_company_reports
       ${whereClause}`,
      params
    )

    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      reports: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    })

  } catch (error) {
    console.error('Error retrieving missing company reports for admin:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve missing company reports' },
      { status: 500 }
    )
  }
}
