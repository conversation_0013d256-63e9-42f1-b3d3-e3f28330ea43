'use client'

interface AuthorizationStatus {
  authorized: boolean
  message: string
  requiresAuth?: boolean
  companyName?: string
  requiredDomain?: string
  userDomain?: string
}

interface CompanyVerificationNoticeProps {
  authStatus: AuthorizationStatus
  className?: string
}

export function CompanyVerificationNotice({ 
  authStatus, 
  className = '' 
}: CompanyVerificationNoticeProps) {
  if (authStatus.authorized) {
    return null
  }

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <svg className="w-5 h-5 text-yellow-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800 mb-1">
            General Information
          </h3>

          <div className="space-y-2">
            <div className="text-xs text-yellow-700">
              <strong>To verify benefits:</strong> You must be an employee of this company with a matching email domain. Only employees can confirm or dispute benefit information.
            </div>

            <div className="text-xs text-yellow-700">
              <strong>About &quot;Pending&quot; status:</strong> Benefits marked as &quot;pending&quot; are waiting for employee verification. They need at least 2 confirmations from employees and more confirmations than disputes to become verified.
            </div>
          </div>

          {authStatus.requiresAuth && (
            <p className="text-xs text-yellow-600 mt-3">
              <a href="/sign-in" className="text-blue-600 hover:text-blue-800 underline font-medium">
                Sign in
              </a> with your company email to verify benefits
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
