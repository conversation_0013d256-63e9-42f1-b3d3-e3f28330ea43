#!/bin/bash

# Database Initialization Script for Kubernetes Deployment

set -e

NAMESPACE="workwell"
POSTGRES_POD=$(kubectl get pods -n ${NAMESPACE} -l app=postgres -o jsonpath='{.items[0].metadata.name}')

echo "🗄️  Initializing WorkWell database..."
echo "📦 PostgreSQL pod: ${POSTGRES_POD}"

# Check if pod exists
if [ -z "$POSTGRES_POD" ]; then
    echo "❌ PostgreSQL pod not found. Make sure PostgreSQL is deployed first."
    exit 1
fi

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
kubectl wait --for=condition=ready pod/${POSTGRES_POD} -n ${NAMESPACE} --timeout=300s

# Copy schema file to the pod
echo "📋 Copying database schema..."
kubectl cp database/schema.sql ${NAMESPACE}/${POSTGRES_POD}:/tmp/schema.sql

# Execute schema
echo "🔧 Creating database schema..."
kubectl exec -n ${NAMESPACE} ${POSTGRES_POD} -- psql -U workwell_user -d workwell -f /tmp/schema.sql

# Copy and execute seed data if it exists
if [ -f "database/seed.sql" ]; then
    echo "🌱 Copying seed data..."
    kubectl cp database/seed.sql ${NAMESPACE}/${POSTGRES_POD}:/tmp/seed.sql
    
    echo "🌱 Inserting seed data..."
    kubectl exec -n ${NAMESPACE} ${POSTGRES_POD} -- psql -U workwell_user -d workwell -f /tmp/seed.sql
fi

# Clean up temporary files
kubectl exec -n ${NAMESPACE} ${POSTGRES_POD} -- rm -f /tmp/schema.sql /tmp/seed.sql

echo "✅ Database initialization completed successfully!"
echo ""
echo "📋 Database connection details:"
echo "   Host: postgres.${NAMESPACE}.svc.cluster.local"
echo "   Port: 5432"
echo "   Database: workwell"
echo "   Username: workwell_user"
echo ""
echo "🔧 To connect to the database from within the cluster:"
echo "   kubectl exec -it ${POSTGRES_POD} -n ${NAMESPACE} -- psql -U workwell_user -d workwell"
