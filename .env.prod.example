# Database
POSTGRES_USER=benefitlens_user
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_DB=benefitlens

# Local Authentication
USE_LOCAL_AUTH=true
SESSION_SECRET=your_random_session_secret_here

# Cache Configuration
CACHE_TYPE=postgresql

# Email (Mailgun Configuration)
SMTP_HOST=smtp.eu.mailgun.org
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_mailgun_api_key_here
FROM_EMAIL=<EMAIL>

# App Configuration
NEXT_PUBLIC_APP_URL=https://www.benefitlens.de
LOG_LEVEL=warn
