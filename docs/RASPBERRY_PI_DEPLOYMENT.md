# Raspberry Pi Deployment Guide

This guide will help you set up automated deployment of BenefitLens on your Raspberry Pi 5 with automatic updates from GitHub.

## Overview

The deployment setup includes:
- **Docker Compose** for running all services
- **GitHub Actions** with self-hosted runner for CI/CD
- **Watchtower** for automatic container updates
- **Portainer** for container management
- **Automated backups** and monitoring

## Prerequisites

- Raspberry Pi 5 with Raspberry Pi OS (64-bit recommended)
- 16GB RAM and 500GB NVMe SSD (you have this! 🎉)
- Internet connection
- GitHub repository access
- Docker Hub account (free tier is sufficient)

## Step 1: Initial Pi Setup

1. **Flash Raspberry Pi OS** to your NVMe SSD
2. **Enable SSH** and connect to your Pi
3. **Run the setup script**:
   ```bash
   # Clone your repository
   git clone https://github.com/your-username/benefitlens.git
   cd benefitlens
   
   # Make setup script executable and run it
   chmod +x scripts/setup-pi-deployment.sh
   ./scripts/setup-pi-deployment.sh
   ```

4. **Reboot your Pi**:
   ```bash
   sudo reboot
   ```

## Step 2: Configure Environment Variables

1. **Copy and edit the production environment file**:
   ```bash
   cd ~/benefitlens
   cp .env.production .env.prod
   nano .env.prod
   ```

2. **Fill in your actual values**:
   ```bash
   # Database
   POSTGRES_PASSWORD=your_very_secure_password_123
   
   # Application
   APP_URL=http://*************:3000  # Your Pi's IP
   SESSION_SECRET=your_very_long_random_session_secret_at_least_32_chars
   
   # Email (optional)
   FROM_EMAIL=<EMAIL>
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-gmail-app-password
   ```

## Step 3: Set Up Docker Hub

1. **Create Docker Hub account** (if you don't have one)
2. **Create a repository** named `benefitlens`
3. **Note your Docker Hub username** for later

## Step 4: Set Up GitHub Actions Self-Hosted Runner

1. **Go to your GitHub repository** → Settings → Actions → Runners
2. **Click "New self-hosted runner"**
3. **Select Linux ARM64**
4. **Run the commands on your Pi**:
   ```bash
   cd ~/actions-runner
   
   # Download (replace URL with the one from GitHub)
   curl -o actions-runner-linux-arm64-2.311.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.311.0/actions-runner-linux-arm64-2.311.0.tar.gz
   
   # Extract
   tar xzf ./actions-runner-linux-arm64-2.311.0.tar.gz
   
   # Configure (use the token from GitHub)
   ./config.sh --url https://github.com/your-username/benefitlens --token YOUR_TOKEN
   
   # Install as service
   sudo ./svc.sh install
   sudo ./svc.sh start
   ```

## Step 5: Configure GitHub Secrets

In your GitHub repository, go to Settings → Secrets and variables → Actions, and add:

```
DOCKER_USERNAME=your_docker_hub_username
DOCKER_PASSWORD=your_docker_hub_password
POSTGRES_PASSWORD=your_very_secure_password_123
SESSION_SECRET=your_very_long_random_session_secret_at_least_32_chars
APP_URL=http://*************:3000
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-gmail-app-password
```

## Step 6: First Deployment

1. **Push to main branch** to trigger the deployment:
   ```bash
   git add .
   git commit -m "Set up Pi deployment"
   git push origin main
   ```

2. **Monitor the deployment** in GitHub Actions tab

3. **Check if services are running**:
   ```bash
   docker ps
   docker-compose -f docker-compose.prod.yml logs -f
   ```

## Step 7: Access Your Application

- **BenefitLens App**: http://your-pi-ip:3000
- **Portainer** (Container Management): http://your-pi-ip:9000
- **Adminer** (Database Management): http://your-pi-ip:8080

## Automatic Updates

Your setup now includes:

1. **GitHub Actions CI/CD**: Automatically builds and deploys when you push to main
2. **Watchtower**: Checks for new container images every 5 minutes
3. **Health Checks**: Monitors application health
4. **Automatic Backups**: Daily database backups at 2 AM

## Monitoring and Maintenance

### Check Application Status
```bash
# Check all containers
docker ps

# Check application logs
docker logs workwell-app -f

# Check database logs
docker logs workwell-postgres -f

# Check system resources
htop
```

### Manual Backup
```bash
cd ~/workwell
./backup.sh
```

### Update Application Manually
```bash
cd ~/benefitlens
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### Restart Services
```bash
# Restart all services
sudo systemctl restart benefitlens

# Or restart specific containers
docker-compose -f docker-compose.prod.yml restart app
```

## Troubleshooting

### Container Won't Start
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs app

# Check environment variables
docker-compose -f docker-compose.prod.yml config
```

### Database Issues
```bash
# Connect to database
docker exec -it benefitlens-postgres psql -U benefitlens_user -d benefitlens

# Check database status
docker exec benefitlens-postgres pg_isready -U benefitlens_user -d benefitlens
```

### GitHub Actions Runner Issues
```bash
# Check runner status
cd ~/actions-runner
sudo ./svc.sh status

# Restart runner
sudo ./svc.sh stop
sudo ./svc.sh start
```

## Performance Optimization

Your Pi 5 with 16GB RAM is very powerful, but here are some optimizations:

1. **Use NVMe SSD** for Docker volumes (you're already doing this!)
2. **Monitor resource usage** with Portainer
3. **Adjust container resources** if needed in docker-compose.prod.yml
4. **Enable swap** if you plan to run multiple applications

## Security Considerations

1. **Change default passwords** in .env.prod
2. **Use strong session secrets**
3. **Keep your Pi updated**: `sudo apt update && sudo apt upgrade`
4. **Consider setting up a firewall**: `sudo ufw enable`
5. **Use SSH keys** instead of passwords

## Next Steps

1. **Set up domain name** and SSL certificate
2. **Configure reverse proxy** (nginx) for better performance
3. **Set up monitoring** (Grafana + Prometheus)
4. **Configure automated SSL** with Let's Encrypt

Your Pi deployment is now ready for production use! 🚀
