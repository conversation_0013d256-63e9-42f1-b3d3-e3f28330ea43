# Testing Multi-Location System

This document outlines the comprehensive testing strategy for the multi-location system implementation.

## Test Coverage

### 1. Unit Tests

#### Location Normalization (`src/__tests__/location-normalization.test.ts`)
- ✅ German city name normalization (München → Munich, Germany)
- ✅ English city name normalization
- ✅ Cities with country already specified
- ✅ Database mapping usage
- ✅ Empty location handling
- ✅ German city pattern detection
- ✅ International city handling
- ✅ Unknown location graceful handling
- ✅ Location suggestions functionality
- ✅ Suggestion deduplication
- ✅ Location mapping persistence

#### Database Functions (`src/__tests__/database-locations.test.ts`)
- ✅ Company location retrieval
- ✅ Location addition with normalization
- ✅ Primary/headquarters flag management
- ✅ Location updates
- ✅ Location removal
- ✅ Error handling for non-existent locations
- ✅ Cache invalidation

#### React Components (`src/__tests__/location-components.test.tsx`)
- ✅ LocationInput component rendering
- ✅ User input handling
- ✅ Suggestion fetching and display
- ✅ Suggestion selection
- ✅ Error state display
- ✅ Disabled state handling
- ✅ MultiLocationInput component
- ✅ Location addition/removal
- ✅ Location type management
- ✅ Primary/headquarters toggles
- ✅ Maximum location limits

### 2. Integration Tests

#### Database Integration (`scripts/test-multi-location-system.js`)
- ✅ Database schema validation
- ✅ Table and index existence
- ✅ Location normalization end-to-end
- ✅ Company creation with locations
- ✅ Multi-location management
- ✅ Search functionality across locations
- ✅ Data integrity constraints
- ✅ Foreign key relationships
- ✅ Primary location uniqueness

### 3. API Tests

#### Location Suggestions API
```bash
# Test location suggestions endpoint
curl "http://localhost:3000/api/locations/suggestions?q=ber"
```

Expected response:
```json
{
  "suggestions": [
    {
      "raw": "ber",
      "normalized": "Berlin, Germany",
      "city": "Berlin",
      "country": "Germany",
      "countryCode": "DE"
    }
  ],
  "query": "ber",
  "count": 1
}
```

#### Company Locations API
```bash
# Get company locations
curl "http://localhost:3000/api/companies/{company-id}/locations"

# Add company location
curl -X POST "http://localhost:3000/api/companies/{company-id}/locations" \
  -H "Content-Type: application/json" \
  -d '{
    "location_raw": "Munich",
    "location_type": "office",
    "is_primary": false,
    "is_headquarters": false
  }'

# Update company location
curl -X PATCH "http://localhost:3000/api/companies/{company-id}/locations" \
  -H "Content-Type: application/json" \
  -d '{
    "location_id": "{location-id}",
    "is_primary": true
  }'

# Remove company location
curl -X DELETE "http://localhost:3000/api/companies/{company-id}/locations?location_id={location-id}"
```

### 4. Frontend Tests

#### Manual Testing Checklist

**Location Input Component:**
- [ ] Type in location input and verify suggestions appear
- [ ] Select a suggestion and verify it populates the input
- [ ] Test with German city names (München, Köln, etc.)
- [ ] Test with international cities (London, Paris, etc.)
- [ ] Verify debouncing (suggestions don't fire on every keystroke)
- [ ] Test keyboard navigation (arrow keys, enter, escape)
- [ ] Test clear button functionality

**Multi-Location Input Component:**
- [ ] Add multiple locations to a company
- [ ] Set primary and headquarters flags
- [ ] Change location types (office, headquarters, branch, remote)
- [ ] Remove locations
- [ ] Verify primary location constraints (only one primary)
- [ ] Verify headquarters constraints (only one headquarters)
- [ ] Test maximum location limits

**Company Search:**
- [ ] Search by city name (Berlin, Munich, etc.)
- [ ] Search by country (Germany, United Kingdom, etc.)
- [ ] Search by normalized location names
- [ ] Verify results include companies with matching locations
- [ ] Test location filter in advanced search

**Company Display:**
- [ ] Company cards show primary location + additional count
- [ ] Company detail pages show all locations with badges
- [ ] Location badges display correctly (HQ, Primary, etc.)
- [ ] Location types display appropriately

**Admin Interface:**
- [ ] Create company with multiple locations
- [ ] Edit company locations
- [ ] Add/remove locations from existing companies
- [ ] Verify location normalization in admin forms
- [ ] Test location search in admin company list

### 5. Performance Tests

#### Database Performance
```sql
-- Test location search performance
EXPLAIN ANALYZE
SELECT DISTINCT c.id, c.name
FROM companies c
LEFT JOIN company_locations cl ON c.id = cl.company_id
WHERE c.location ILIKE '%Berlin%' 
   OR cl.location_raw ILIKE '%Berlin%'
   OR cl.location_normalized ILIKE '%Berlin%'
   OR cl.city ILIKE '%Berlin%'
   OR cl.country ILIKE '%Berlin%';

-- Location suggestions are now handled by the comprehensive city library
-- No database queries needed for location suggestions
```

#### API Performance
```bash
# Test suggestion API response time
time curl "http://localhost:3000/api/locations/suggestions?q=ber"

# Test company search with location filter
time curl "http://localhost:3000/api/companies?location=Berlin"
```

### 6. Data Migration Tests

#### Pre-Migration Validation
```sql
-- Count companies with location data
SELECT COUNT(*) FROM companies WHERE location IS NOT NULL AND location != '';

-- Sample existing location formats
SELECT DISTINCT location FROM companies WHERE location IS NOT NULL ORDER BY location;
```

#### Migration Testing
```bash
# Run migration dry run
node scripts/migrate-company-locations.js

# Run actual migration
node scripts/migrate-company-locations.js --live
```

#### Post-Migration Validation
```sql
-- Verify all companies have location entries
SELECT 
  c.name,
  c.location as legacy_location,
  COUNT(cl.id) as location_count
FROM companies c
LEFT JOIN company_locations cl ON c.id = cl.company_id
WHERE c.location IS NOT NULL
GROUP BY c.id, c.name, c.location
HAVING COUNT(cl.id) = 0;

-- Check location normalization quality
SELECT 
  location_raw,
  location_normalized,
  COUNT(*) as usage_count
FROM company_locations
GROUP BY location_raw, location_normalized
ORDER BY usage_count DESC;
```

### 7. Error Handling Tests

#### API Error Scenarios
- [ ] Invalid company ID in location endpoints
- [ ] Missing required fields in location creation
- [ ] Unauthorized access to company location management
- [ ] Database connection failures
- [ ] Invalid location data formats

#### Frontend Error Scenarios
- [ ] Network failures during suggestion fetching
- [ ] Invalid API responses
- [ ] Component error boundaries
- [ ] Form validation errors

### 8. Browser Compatibility Tests

#### Supported Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

#### Mobile Testing
- [ ] iOS Safari
- [ ] Android Chrome
- [ ] Responsive design on various screen sizes

### 9. Accessibility Tests

#### WCAG Compliance
- [ ] Keyboard navigation for location inputs
- [ ] Screen reader compatibility
- [ ] Color contrast for location badges
- [ ] Focus indicators
- [ ] ARIA labels for interactive elements

### 10. Running Tests

#### Unit Tests
```bash
npm test
npm run test:watch
npm run test:coverage
```

#### Integration Tests
```bash
node scripts/test-multi-location-system.js
```

#### E2E Tests (if implemented)
```bash
npm run test:e2e
```

### 11. Test Data

#### Sample Locations for Testing
- German cities: Berlin, München, Hamburg, Frankfurt, Stuttgart
- International: London, Paris, Amsterdam, Zurich, Vienna
- Edge cases: "Remote", "Multiple Locations", "TBD"

#### Test Companies
- Single location companies
- Multi-location companies
- Companies with headquarters vs. offices
- Companies with remote locations

### 12. Monitoring and Alerts

#### Production Monitoring
- Location suggestion API response times
- Search performance with location filters
- Database query performance
- Error rates for location-related operations

#### Success Metrics
- Location normalization accuracy
- User adoption of multi-location features
- Search result relevance improvements
- Admin efficiency in location management
