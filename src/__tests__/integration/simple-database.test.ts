/**
 * Simple Database Integration Test
 * Tests basic database connectivity and operations
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { query } from '@/lib/local-db'

describe('Simple Database Integration Test', () => {
  beforeAll(async () => {
    console.log('🧪 Starting simple database integration test')
  })

  afterAll(async () => {
    console.log('🧪 Simple database integration test complete')
  })

  it('should connect to the database', async () => {
    const result = await query('SELECT NOW() as current_time')
    
    expect(result).toBeDefined()
    expect(result.rows).toBeDefined()
    expect(result.rows.length).toBe(1)
    expect(result.rows[0]).toHaveProperty('current_time')
    expect(result.rows[0].current_time).toBeInstanceOf(Date)
  })

  it('should verify required tables exist', async () => {
    const result = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'companies', 'benefits', 'benefit_categories')
      ORDER BY table_name
    `)
    
    expect(result.rows.length).toBe(4)
    
    const tableNames = result.rows.map(r => r.table_name)
    expect(tableNames).toContain('users')
    expect(tableNames).toContain('companies')
    expect(tableNames).toContain('benefits')
    expect(tableNames).toContain('benefit_categories')
  })

  it('should be able to query benefit categories', async () => {
    const result = await query('SELECT id, name, display_name FROM benefit_categories LIMIT 5')
    
    expect(result).toBeDefined()
    expect(result.rows).toBeDefined()
    expect(result.rows.length).toBeGreaterThan(0)
    
    // Verify structure
    const category = result.rows[0]
    expect(category).toHaveProperty('id')
    expect(category).toHaveProperty('name')
    expect(category).toHaveProperty('display_name')
    expect(typeof category.id).toBe('string')
    expect(typeof category.name).toBe('string')
    expect(typeof category.display_name).toBe('string')
  })

  it('should be able to query benefits', async () => {
    const result = await query('SELECT id, name, description, category_id FROM benefits LIMIT 5')
    
    expect(result).toBeDefined()
    expect(result.rows).toBeDefined()
    expect(result.rows.length).toBeGreaterThan(0)
    
    // Verify structure
    const benefit = result.rows[0]
    expect(benefit).toHaveProperty('id')
    expect(benefit).toHaveProperty('name')
    expect(benefit).toHaveProperty('category_id')
    expect(typeof benefit.id).toBe('string')
    expect(typeof benefit.name).toBe('string')
    expect(typeof benefit.category_id).toBe('string')
  })

  it('should be able to query companies', async () => {
    const result = await query('SELECT id, name, domain, industry, size FROM companies LIMIT 5')
    
    expect(result).toBeDefined()
    expect(result.rows).toBeDefined()
    expect(result.rows.length).toBeGreaterThan(0)
    
    // Verify structure
    const company = result.rows[0]
    expect(company).toHaveProperty('id')
    expect(company).toHaveProperty('name')
    expect(company).toHaveProperty('industry')
    expect(company).toHaveProperty('size')
    expect(typeof company.id).toBe('string')
    expect(typeof company.name).toBe('string')
    expect(typeof company.industry).toBe('string')
    expect(typeof company.size).toBe('string')
  })

  it('should be able to insert and delete test data', async () => {
    // Insert test category
    const insertResult = await query(`
      INSERT INTO benefit_categories (name, display_name, icon) 
      VALUES ('test_simple', 'Test Simple', '🧪')
      RETURNING id, name
    `)
    
    expect(insertResult.rows.length).toBe(1)
    const categoryId = insertResult.rows[0].id
    expect(typeof categoryId).toBe('string')
    
    // Verify it was inserted
    const selectResult = await query(
      'SELECT id, name, display_name FROM benefit_categories WHERE id = $1',
      [categoryId]
    )
    
    expect(selectResult.rows.length).toBe(1)
    expect(selectResult.rows[0].name).toBe('test_simple')
    expect(selectResult.rows[0].display_name).toBe('Test Simple')
    
    // Clean up
    const deleteResult = await query(
      'DELETE FROM benefit_categories WHERE id = $1',
      [categoryId]
    )
    
    expect(deleteResult.rowCount).toBe(1)
    
    // Verify it was deleted
    const verifyResult = await query(
      'SELECT id FROM benefit_categories WHERE id = $1',
      [categoryId]
    )
    
    expect(verifyResult.rows.length).toBe(0)
  })
})
