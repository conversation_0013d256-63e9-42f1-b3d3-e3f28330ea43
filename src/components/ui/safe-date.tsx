'use client'

import { useState, useEffect } from 'react'

interface SafeDateProps {
  date: string | Date
  format?: 'date' | 'datetime' | 'time'
  className?: string
  fallback?: string
}

/**
 * SafeDate component prevents hydration mismatches by only rendering
 * formatted dates on the client side after hydration is complete.
 */
export function SafeDate({ 
  date, 
  format = 'date', 
  className = '', 
  fallback = 'Loading...' 
}: SafeDateProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <span className={className}>{fallback}</span>
  }

  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return <span className={className}>Invalid date</span>
  }

  let formattedDate: string

  switch (format) {
    case 'datetime':
      formattedDate = dateObj.toLocaleString()
      break
    case 'time':
      formattedDate = dateObj.toLocaleTimeString()
      break
    case 'date':
    default:
      formattedDate = dateObj.toLocaleDateString()
      break
  }

  return <span className={className}>{formattedDate}</span>
}

/**
 * Hook for safe date formatting that prevents hydration mismatches
 */
export function useSafeDate(date: string | Date, format: 'date' | 'datetime' | 'time' = 'date') {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid date'
  }

  switch (format) {
    case 'datetime':
      return dateObj.toLocaleString()
    case 'time':
      return dateObj.toLocaleTimeString()
    case 'date':
    default:
      return dateObj.toLocaleDateString()
  }
}
