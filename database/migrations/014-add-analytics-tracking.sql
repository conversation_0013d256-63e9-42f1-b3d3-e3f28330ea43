-- Migration: Add analytics tracking tables
-- This migration adds comprehensive analytics tracking for real data collection
-- Date: 2025-08-02

-- Company page views tracking
CREATE TABLE company_page_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- NULL for anonymous views
    session_id VARCHAR(255), -- For tracking anonymous sessions
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search queries tracking
CREATE TABLE search_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- NULL for anonymous searches
    session_id VARCHAR(255), -- For tracking anonymous sessions
    results_count INTEGER DEFAULT 0,
    filters_applied JSONB, -- Store applied filters as JSON
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefit search interactions (when users click on benefits in search results)
CREATE TABLE benefit_search_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_query_id UUID REFERENCES search_queries(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) CHECK (interaction_type IN ('view', 'click', 'verify', 'dispute')) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics summary tables for performance (daily aggregates)
CREATE TABLE daily_analytics_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    total_company_views INTEGER DEFAULT 0,
    total_searches INTEGER DEFAULT 0,
    total_benefit_interactions INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    unique_searchers INTEGER DEFAULT 0,
    top_searched_benefits JSONB, -- Array of {benefit_id, benefit_name, search_count}
    top_viewed_companies JSONB, -- Array of {company_id, company_name, view_count}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date)
);

-- Company analytics summary (for quick company-specific metrics)
CREATE TABLE company_analytics_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    page_views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    benefit_interactions INTEGER DEFAULT 0,
    search_appearances INTEGER DEFAULT 0, -- How many times company appeared in search results
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, date)
);

-- Indexes for better performance
CREATE INDEX idx_company_page_views_company_id ON company_page_views(company_id);
CREATE INDEX idx_company_page_views_user_id ON company_page_views(user_id);
CREATE INDEX idx_company_page_views_session_id ON company_page_views(session_id);
CREATE INDEX idx_company_page_views_created_at ON company_page_views(created_at);

CREATE INDEX idx_search_queries_user_id ON search_queries(user_id);
CREATE INDEX idx_search_queries_session_id ON search_queries(session_id);
CREATE INDEX idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX idx_search_queries_query_text ON search_queries USING gin(to_tsvector('english', query_text));

CREATE INDEX idx_benefit_search_interactions_search_query_id ON benefit_search_interactions(search_query_id);
CREATE INDEX idx_benefit_search_interactions_benefit_id ON benefit_search_interactions(benefit_id);
CREATE INDEX idx_benefit_search_interactions_company_id ON benefit_search_interactions(company_id);
CREATE INDEX idx_benefit_search_interactions_user_id ON benefit_search_interactions(user_id);
CREATE INDEX idx_benefit_search_interactions_created_at ON benefit_search_interactions(created_at);

CREATE INDEX idx_daily_analytics_summary_date ON daily_analytics_summary(date);
CREATE INDEX idx_company_analytics_summary_company_id ON company_analytics_summary(company_id);
CREATE INDEX idx_company_analytics_summary_date ON company_analytics_summary(date);

-- Triggers for updated_at
CREATE TRIGGER update_daily_analytics_summary_updated_at BEFORE UPDATE ON daily_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_analytics_summary_updated_at BEFORE UPDATE ON company_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security
ALTER TABLE company_page_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_search_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_analytics_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_analytics_summary ENABLE ROW LEVEL SECURITY;

-- RLS Policies (Admin can see all, users can see their own data)
CREATE POLICY "Admin can view all company page views" ON company_page_views
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all search queries" ON search_queries
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all benefit interactions" ON benefit_search_interactions
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all analytics summaries" ON daily_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all company analytics" ON company_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic

-- Functions for analytics aggregation
CREATE OR REPLACE FUNCTION update_daily_analytics_summary(target_date DATE DEFAULT CURRENT_DATE)
RETURNS void AS $$
BEGIN
    INSERT INTO daily_analytics_summary (
        date,
        total_company_views,
        total_searches,
        total_benefit_interactions,
        unique_visitors,
        unique_searchers,
        top_searched_benefits,
        top_viewed_companies
    )
    SELECT
        target_date,
        COALESCE(company_views.total_views, 0),
        COALESCE(searches.total_searches, 0),
        COALESCE(interactions.total_interactions, 0),
        COALESCE(company_views.unique_visitors, 0),
        COALESCE(searches.unique_searchers, 0),
        COALESCE(top_benefits.benefits, '[]'::jsonb),
        COALESCE(top_companies.companies, '[]'::jsonb)
    FROM (
        -- Company views for the day
        SELECT
            COUNT(*) as total_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE DATE(created_at) = target_date
    ) company_views
    CROSS JOIN (
        -- Searches for the day
        SELECT
            COUNT(*) as total_searches,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_searchers
        FROM search_queries
        WHERE DATE(created_at) = target_date
    ) searches
    CROSS JOIN (
        -- Benefit interactions for the day
        SELECT
            COUNT(*) as total_interactions
        FROM benefit_search_interactions
        WHERE DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        -- Top searched benefits
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'benefit_id', b.id,
                    'benefit_name', b.name,
                    'search_count', benefit_searches.search_count
                )
                ORDER BY benefit_searches.search_count DESC
            ) FILTER (WHERE benefit_searches.search_count > 0), '[]'::jsonb) as benefits
        FROM (
            SELECT
                bsi.benefit_id,
                COUNT(*) as search_count
            FROM benefit_search_interactions bsi
            JOIN search_queries sq ON bsi.search_query_id = sq.id
            WHERE DATE(sq.created_at) = target_date
            GROUP BY bsi.benefit_id
            ORDER BY search_count DESC
            LIMIT 10
        ) benefit_searches
        JOIN benefits b ON benefit_searches.benefit_id = b.id
    ) top_benefits
    CROSS JOIN (
        -- Top viewed companies
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'company_id', c.id,
                    'company_name', c.name,
                    'view_count', company_views.view_count
                )
                ORDER BY company_views.view_count DESC
            ) FILTER (WHERE company_views.view_count > 0), '[]'::jsonb) as companies
        FROM (
            SELECT
                cpv.company_id,
                COUNT(*) as view_count
            FROM company_page_views cpv
            WHERE DATE(cpv.created_at) = target_date
            GROUP BY cpv.company_id
            ORDER BY view_count DESC
            LIMIT 10
        ) company_views
        JOIN companies c ON company_views.company_id = c.id
    ) top_companies
    ON CONFLICT (date) DO UPDATE SET
        total_company_views = EXCLUDED.total_company_views,
        total_searches = EXCLUDED.total_searches,
        total_benefit_interactions = EXCLUDED.total_benefit_interactions,
        unique_visitors = EXCLUDED.unique_visitors,
        unique_searchers = EXCLUDED.unique_searchers,
        top_searched_benefits = EXCLUDED.top_searched_benefits,
        top_viewed_companies = EXCLUDED.top_viewed_companies,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to update company-specific analytics
CREATE OR REPLACE FUNCTION update_company_analytics_summary(target_company_id UUID, target_date DATE DEFAULT CURRENT_DATE)
RETURNS void AS $$
BEGIN
    INSERT INTO company_analytics_summary (
        company_id,
        date,
        page_views,
        unique_visitors,
        benefit_interactions,
        search_appearances
    )
    SELECT
        target_company_id,
        target_date,
        COALESCE(views.page_views, 0),
        COALESCE(views.unique_visitors, 0),
        COALESCE(interactions.benefit_interactions, 0),
        COALESCE(appearances.search_appearances, 0)
    FROM (
        SELECT
            COUNT(*) as page_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) views
    CROSS JOIN (
        SELECT
            COUNT(*) as benefit_interactions
        FROM benefit_search_interactions
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        SELECT
            COUNT(DISTINCT sq.id) as search_appearances
        FROM search_queries sq
        JOIN benefit_search_interactions bsi ON sq.id = bsi.search_query_id
        WHERE bsi.company_id = target_company_id
        AND DATE(sq.created_at) = target_date
    ) appearances
    ON CONFLICT (company_id, date) DO UPDATE SET
        page_views = EXCLUDED.page_views,
        unique_visitors = EXCLUDED.unique_visitors,
        benefit_interactions = EXCLUDED.benefit_interactions,
        search_appearances = EXCLUDED.search_appearances,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;
