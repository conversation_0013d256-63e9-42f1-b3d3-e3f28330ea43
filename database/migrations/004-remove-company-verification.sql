-- Migration: Remove company verification workflow
-- This migration removes the manual company verification system since magic link authentication
-- now provides automatic email domain verification

-- Drop company verification tokens table
DROP TABLE IF EXISTS company_verification_tokens CASCADE;

-- Drop company users table since we no longer need manual verification
-- Domain matching through email authentication is now sufficient
DROP TABLE IF EXISTS company_users CASCADE;

-- Remove any indexes related to these tables
DROP INDEX IF EXISTS idx_company_verification_tokens_token;
DROP INDEX IF EXISTS idx_company_verification_tokens_user_email;
DROP INDEX IF EXISTS idx_company_verification_tokens_company_id;
DROP INDEX IF EXISTS idx_company_verification_tokens_expires_at;

DROP INDEX IF EXISTS idx_company_users_company_id;
DROP INDEX IF EXISTS idx_company_users_email;

-- Add comment to document the change
COMMENT ON TABLE companies IS 'Companies table - user access is now controlled by email domain matching instead of manual verification';

-- Clean up any orphaned data that might reference the removed tables
-- (This is safe since we're dropping the tables anyway, but good practice)

-- Update any admin notes or logs if needed
-- (No specific cleanup needed for this migration)

-- Add a note about the migration
INSERT INTO migration_log (migration_name, description, applied_at) 
VALUES (
  '004-remove-company-verification', 
  'Removed manual company verification workflow in favor of automatic email domain matching',
  NOW()
) ON CONFLICT DO NOTHING;

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
