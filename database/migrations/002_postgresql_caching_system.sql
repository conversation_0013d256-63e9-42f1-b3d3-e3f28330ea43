-- PostgreSQL Caching System
-- Replaces Redis caching with PostgreSQL-based solutions

-- Create cache table for general key-value caching
CREATE TABLE IF NOT EXISTS cache_store (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_key VARCHAR(255) NOT NULL UNIQUE,
    cache_value JSONB NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for cache performance
CREATE INDEX IF NOT EXISTS idx_cache_store_key ON cache_store(cache_key);
CREATE INDEX IF NOT EXISTS idx_cache_store_expires_at ON cache_store(expires_at);
CREATE INDEX IF NOT EXISTS idx_cache_store_created_at ON cache_store(created_at);

-- CSRF token storage table
CREATE TABLE IF NOT EXISTS csrf_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(session_id)
);

CREATE INDEX IF NOT EXISTS idx_csrf_tokens_session_id ON csrf_tokens(session_id);
CREATE INDEX IF NOT EXISTS idx_csrf_tokens_expires_at ON csrf_tokens(expires_at);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    identifier VARCHAR(255) NOT NULL,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    request_count INTEGER DEFAULT 1,
    request_timestamps JSONB DEFAULT '[]'::jsonb,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(identifier, window_start)
);

CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier ON rate_limits(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limits_expires_at ON rate_limits(expires_at);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window_start ON rate_limits(window_start);

-- Materialized views for frequently accessed data
-- Companies with benefits (refreshed periodically)
CREATE MATERIALIZED VIEW IF NOT EXISTS companies_with_benefits_cache AS
SELECT 
    c.id,
    c.name,
    c.description,
    c.website,
    c.logo_url,
    c.size,
    c.industry,
    c.founded_year,
    c.verification_status,
    c.created_at,
    c.updated_at,
    COALESCE(
        json_agg(
            DISTINCT jsonb_build_object(
                'id', b.id,
                'name', b.name,
                'category_id', b.category_id,
                'icon', b.icon,
                'description', b.description,
                'category_name', bc.name,
                'category_display_name', bc.display_name,
                'category_icon', bc.icon
            )
        ) FILTER (WHERE b.id IS NOT NULL),
        '[]'::json
    ) as benefits,
    COALESCE(
        json_agg(
            DISTINCT jsonb_build_object(
                'id', cl.id,
                'type', cl.type,
                'city', cl.city,
                'state', cl.state,
                'country', cl.country,
                'is_remote', cl.is_remote,
                'is_headquarters', cl.is_headquarters
            )
        ) FILTER (WHERE cl.id IS NOT NULL),
        '[]'::json
    ) as locations
FROM companies c
LEFT JOIN company_benefits cb ON c.id = cb.company_id
LEFT JOIN benefits b ON cb.benefit_id = b.id
LEFT JOIN benefit_categories bc ON b.category_id = bc.id
LEFT JOIN company_locations cl ON c.id = cl.company_id
WHERE c.verification_status = 'verified'
GROUP BY c.id, c.name, c.description, c.website, c.logo_url, c.size, 
         c.industry, c.founded_year, c.verification_status, c.created_at, c.updated_at;

-- Index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_companies_with_benefits_cache_id 
ON companies_with_benefits_cache(id);

CREATE INDEX IF NOT EXISTS idx_companies_with_benefits_cache_name 
ON companies_with_benefits_cache USING gin(to_tsvector('english', name));

-- Benefits with categories cache
CREATE MATERIALIZED VIEW IF NOT EXISTS benefits_with_categories_cache AS
SELECT 
    b.id,
    b.name,
    b.category_id,
    b.icon,
    b.description,
    b.created_at,
    bc.name as category_name,
    bc.display_name as category_display_name,
    bc.icon as category_icon,
    bc.description as category_description,
    COUNT(cb.company_id) as company_count
FROM benefits b
LEFT JOIN benefit_categories bc ON b.category_id = bc.id
LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
LEFT JOIN companies c ON cb.company_id = c.id AND c.verification_status = 'verified'
GROUP BY b.id, b.name, b.category_id, b.icon, b.description, b.created_at,
         bc.name, bc.display_name, bc.icon, bc.description
ORDER BY b.name;

CREATE UNIQUE INDEX IF NOT EXISTS idx_benefits_with_categories_cache_id 
ON benefits_with_categories_cache(id);

-- Cache management functions
CREATE OR REPLACE FUNCTION set_cache(key VARCHAR, value JSONB, ttl_seconds INTEGER DEFAULT 3600)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO cache_store (cache_key, cache_value, expires_at)
    VALUES (key, value, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (cache_key) 
    DO UPDATE SET 
        cache_value = EXCLUDED.cache_value,
        expires_at = EXCLUDED.expires_at,
        updated_at = NOW();
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_cache(key VARCHAR)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT cache_value INTO result
    FROM cache_store
    WHERE cache_key = key AND expires_at > NOW();
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION delete_cache(key VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key = key;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count > 0;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION clear_cache_pattern(pattern VARCHAR)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key LIKE pattern;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Cache cleanup function
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Also cleanup expired CSRF tokens
    DELETE FROM csrf_tokens WHERE expires_at < NOW();
    
    -- Cleanup expired rate limits
    DELETE FROM rate_limits WHERE expires_at < NOW();
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- CSRF token management functions
CREATE OR REPLACE FUNCTION set_csrf_token(session_id VARCHAR, token VARCHAR, ttl_seconds INTEGER DEFAULT 3600)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO csrf_tokens (session_id, token, expires_at)
    VALUES (session_id, token, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (session_id)
    DO UPDATE SET 
        token = EXCLUDED.token,
        expires_at = EXCLUDED.expires_at,
        created_at = NOW();
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_csrf_token(session_id VARCHAR)
RETURNS VARCHAR AS $$
DECLARE
    result VARCHAR;
BEGIN
    SELECT token INTO result
    FROM csrf_tokens
    WHERE session_id = session_id AND expires_at > NOW();
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Materialized view refresh function
CREATE OR REPLACE FUNCTION refresh_cache_views()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY companies_with_benefits_cache;
    REFRESH MATERIALIZED VIEW CONCURRENTLY benefits_with_categories_cache;
    
    -- Log the refresh
    INSERT INTO activity_log (
        event_type,
        event_description,
        created_at
    ) VALUES (
        'cache_refresh',
        'Materialized views refreshed',
        NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- Update migration log
INSERT INTO migration_log (migration_name, description) VALUES
('002_postgresql_caching_system', 'Added PostgreSQL-based caching system with materialized views and cache management functions')
ON CONFLICT (migration_name) DO NOTHING;
