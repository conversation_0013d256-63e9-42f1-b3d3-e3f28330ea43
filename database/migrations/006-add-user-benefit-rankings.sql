-- Migration: Add user benefit rankings table
-- This migration adds the ability for users to rank benefits by importance
-- Date: 2025-08-01

-- User benefit rankings table
CREATE TABLE user_benefit_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10), -- 1 = most important, 10 = least important
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, benefit_id)
);

-- Indexes for better performance
CREATE INDEX idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);
CREATE INDEX idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);
CREATE INDEX idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);
CREATE INDEX idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);

-- Trigger for updated_at
CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON user_benefit_rankings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security
ALTER TABLE user_benefit_rankings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own benefit rankings" ON user_benefit_rankings
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can insert their own benefit rankings" ON user_benefit_rankings
    FOR INSERT WITH CHECK (true); -- Will be restricted by application logic

CREATE POLICY "Users can update their own benefit rankings" ON user_benefit_rankings
    FOR UPDATE USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can delete their own benefit rankings" ON user_benefit_rankings
    FOR DELETE USING (true); -- Will be restricted by application logic
