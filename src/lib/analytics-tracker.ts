import { query } from './local-db'
import { getCurrentUser } from './auth'
import { headers } from 'next/headers'

export interface AnalyticsEvent {
  type: 'company_view' | 'search' | 'benefit_interaction'
  data: CompanyViewEvent | SearchEvent | BenefitInteractionEvent
}

export interface CompanyViewEvent {
  companyId: string
  referrer?: string
}

export interface SearchEvent {
  queryText: string
  resultsCount: number
  filtersApplied?: Record<string, unknown>
}

export interface BenefitInteractionEvent {
  searchQueryId?: string
  benefitId: string
  companyId: string
  interactionType: 'view' | 'click' | 'verify' | 'dispute'
}

export interface SessionInfo {
  userId?: string
  sessionId: string
  ipAddress?: string
  userAgent?: string
}

/**
 * Get session information for analytics tracking
 */
export async function getSessionInfo(): Promise<SessionInfo> {
  const user = await getCurrentUser()
  const headersList = await headers()
  
  // Generate a session ID if not provided (in real app, this would come from session management)
  const sessionId = headersList.get('x-session-id') || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  return {
    userId: user?.id,
    sessionId,
    ipAddress: headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || '127.0.0.1',
    userAgent: headersList.get('user-agent') || 'Unknown'
  }
}

/**
 * Track a company page view
 */
export async function trackCompanyView(companyId: string, referrer?: string): Promise<void> {
  try {
    const sessionInfo = await getSessionInfo()
    
    await query(`
      INSERT INTO company_page_views (
        company_id, user_id, session_id, ip_address, user_agent, referrer
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      companyId,
      sessionInfo.userId || null,
      sessionInfo.sessionId,
      sessionInfo.ipAddress,
      sessionInfo.userAgent,
      referrer || null
    ])
    
    // Update company analytics summary for today
    await query(`
      SELECT update_company_analytics_summary($1, CURRENT_DATE)
    `, [companyId])
    
  } catch (error) {
    console.error('Error tracking company view:', error)
    // Don't throw - analytics tracking should not break the app
  }
}

/**
 * Track a search query
 */
export async function trackSearch(
  queryText: string, 
  resultsCount: number, 
  filtersApplied?: Record<string, unknown>
): Promise<string | null> {
  try {
    const sessionInfo = await getSessionInfo()
    
    const result = await query(`
      INSERT INTO search_queries (
        query_text, user_id, session_id, results_count, filters_applied, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id
    `, [
      queryText,
      sessionInfo.userId || null,
      sessionInfo.sessionId,
      resultsCount,
      filtersApplied ? JSON.stringify(filtersApplied) : null,
      sessionInfo.ipAddress,
      sessionInfo.userAgent
    ])
    
    // Update daily analytics summary
    await query(`
      SELECT update_daily_analytics_summary(CURRENT_DATE)
    `)
    
    return result.rows[0]?.id || null
    
  } catch (error) {
    console.error('Error tracking search:', error)
    return null
  }
}

/**
 * Track a benefit interaction (view, click, verify, dispute)
 */
export async function trackBenefitInteraction(
  benefitId: string,
  companyId: string,
  interactionType: 'view' | 'click' | 'verify' | 'dispute',
  searchQueryId?: string
): Promise<void> {
  try {
    const sessionInfo = await getSessionInfo()

    await query(`
      INSERT INTO benefit_search_interactions (
        search_query_id, benefit_id, company_id, interaction_type, user_id, session_id
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      searchQueryId || null,
      benefitId,
      companyId,
      interactionType,
      sessionInfo.userId || null,
      sessionInfo.sessionId
    ])
    
    // Update analytics summaries
    await Promise.all([
      query(`SELECT update_daily_analytics_summary(CURRENT_DATE)`),
      query(`SELECT update_company_analytics_summary($1, CURRENT_DATE)`, [companyId])
    ])
    
  } catch (error) {
    console.error('Error tracking benefit interaction:', error)
    // Don't throw - analytics tracking should not break the app
  }
}

/**
 * Get analytics data for overview dashboard
 */
export async function getAnalyticsOverview(period: string = '7d') {
  try {
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    // Get aggregated data from summary tables and raw data
    const [overviewResult, trendsResult, activeCompaniesResult] = await Promise.all([
      // Overall metrics
      query(`
        SELECT
          COALESCE(SUM(das.total_company_views), 0) as total_company_views,
          COALESCE(SUM(das.total_searches), 0) as total_searches,
          COALESCE(SUM(das.total_benefit_interactions), 0) as total_benefit_interactions,
          COALESCE(AVG(das.unique_visitors), 0) as avg_daily_visitors
        FROM daily_analytics_summary das
        WHERE das.date >= $1
      `, [startDate.toISOString().split('T')[0]]),

      // Recent trends
      query(`
        SELECT
          date,
          total_company_views,
          total_searches,
          total_benefit_interactions
        FROM daily_analytics_summary
        WHERE date >= $1
        ORDER BY date ASC
      `, [startDate.toISOString().split('T')[0]]),

      // Active companies (companies with actual analytics activity in the period)
      query(`
        SELECT COUNT(DISTINCT company_id) as active_companies
        FROM (
          SELECT DISTINCT company_id
          FROM company_page_views
          WHERE created_at >= $1
          UNION
          SELECT DISTINCT company_id
          FROM benefit_search_interactions
          WHERE created_at >= $1
        ) active_companies_union
      `, [startDate.toISOString()])
    ])

    const overview = overviewResult.rows[0] || {
      total_company_views: 0,
      total_searches: 0,
      total_benefit_interactions: 0,
      avg_daily_visitors: 0
    }

    const activeCompanies = parseInt(activeCompaniesResult.rows[0]?.active_companies || 0)
    
    // Calculate engagement rate (interactions per view)
    const engagementRate = overview.total_company_views > 0 
      ? (overview.total_benefit_interactions / overview.total_company_views) * 100 
      : 0
    
    return {
      overview: {
        total_searches: parseInt(overview.total_searches),
        company_views: parseInt(overview.total_company_views),
        active_companies: activeCompanies,
        avg_engagement: Math.round(engagementRate * 100) / 100
      },
      trends: trendsResult.rows,
      period,
      generated_at: new Date().toISOString(),
      is_demo_data: false
    }
    
  } catch (error) {
    console.error('Error getting analytics overview:', error)
    throw error
  }
}

/**
 * Get search trends analytics
 */
export async function getSearchTrends(period: string = '7d', limit: number = 10) {
  try {
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    const result = await query(`
      SELECT
        b.name as search_term,
        bc.name as category,
        b.icon,
        COUNT(bsi.id) as search_count,
        COUNT(DISTINCT sq.id) as unique_searches,
        COUNT(DISTINCT bsi.company_id) as companies_involved,
        RANK() OVER (ORDER BY COUNT(bsi.id) DESC) as rank
      FROM benefit_search_interactions bsi
      JOIN benefits b ON bsi.benefit_id = b.id
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      LEFT JOIN search_queries sq ON bsi.search_query_id = sq.id
      WHERE bsi.created_at >= $1
      GROUP BY b.id, b.name, bc.name, b.icon
      HAVING COUNT(bsi.id) > 0
      ORDER BY search_count DESC
      LIMIT $2
    `, [startDate.toISOString(), limit])
    
    // Calculate trends (compare with previous period)
    const trends = await Promise.all(result.rows.map(async (row, index) => {
      const previousStartDate = new Date(startDate)
      previousStartDate.setDate(previousStartDate.getDate() - days)
      
      const previousResult = await query(`
        SELECT COUNT(bsi.id) as previous_count
        FROM benefit_search_interactions bsi
        JOIN benefits b ON bsi.benefit_id = b.id
        JOIN search_queries sq ON bsi.search_query_id = sq.id
        WHERE sq.created_at >= $1 AND sq.created_at < $2 AND b.name = $3
      `, [previousStartDate.toISOString(), startDate.toISOString(), row.search_term])
      
      const previousCount = parseInt(previousResult.rows[0]?.previous_count || 0)
      const currentCount = parseInt(row.search_count)
      const change = previousCount > 0 ? Math.round(((currentCount - previousCount) / previousCount) * 100) : 0
      
      return {
        ...row,
        rank: index + 1,
        search_count: currentCount,
        change: Math.max(-100, Math.min(100, change)), // Cap at ±100%
        trend_score: Math.round((currentCount * 10) + (change * 2))
      }
    }))
    
    return {
      trends,
      period,
      total_searches: trends.reduce((sum, trend) => sum + trend.search_count, 0),
      generated_at: new Date().toISOString(),
      is_demo_data: false
    }
    
  } catch (error) {
    console.error('Error getting search trends:', error)
    throw error
  }
}

/**
 * Get top companies analytics
 */
export async function getTopCompanies(period: string = '7d', limit: number = 10) {
  try {
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    const result = await query(`
      SELECT
        c.id,
        c.name,
        cl.location_normalized as location,
        c.industry,
        c.created_at,
        COALESCE(SUM(cas.page_views), 0) as view_count,
        COALESCE(SUM(cas.unique_visitors), 0) as unique_visitors,
        COALESCE(SUM(cas.benefit_interactions), 0) as benefit_interactions,
        COUNT(cb.id) as benefit_count,
        COUNT(CASE WHEN cb.is_verified THEN 1 END) as verified_benefit_count,
        RANK() OVER (ORDER BY COALESCE(SUM(cas.page_views), 0) DESC) as rank
      FROM companies c
      LEFT JOIN company_locations cl ON c.id = cl.company_id AND cl.is_primary = true
      LEFT JOIN company_analytics_summary cas ON c.id = cas.company_id AND cas.date >= $1
      LEFT JOIN company_benefits cb ON c.id = cb.company_id
      GROUP BY c.id, c.name, cl.location_normalized, c.industry, c.created_at
      HAVING COALESCE(SUM(cas.page_views), 0) > 0
      ORDER BY view_count DESC
      LIMIT $2
    `, [startDate.toISOString().split('T')[0], limit])
    
    const companies = result.rows.map((row, index) => {
      const viewCount = parseInt(row.view_count)
      const interactions = parseInt(row.benefit_interactions)
      const benefitCount = parseInt(row.benefit_count)
      const verifiedBenefitCount = parseInt(row.verified_benefit_count)
      
      const engagementRate = viewCount > 0 ? (interactions / viewCount) * 100 : 0
      const benefitCompletionRate = benefitCount > 0 ? (verifiedBenefitCount / benefitCount) * 100 : 0
      
      return {
        ...row,
        rank: index + 1,
        view_count: viewCount,
        engagement_rate: Math.round(engagementRate * 100) / 100,
        benefit_completion_rate: Math.round(benefitCompletionRate)
      }
    })
    
    return {
      companies,
      period,
      total_views: companies.reduce((sum, company) => sum + company.view_count, 0),
      generated_at: new Date().toISOString(),
      is_demo_data: false
    }
    
  } catch (error) {
    console.error('Error getting top companies:', error)
    throw error
  }
}
