'use client'

import { Suspense } from 'react'
import { MagicLinkVerification } from '@/components/auth/magic-link-verification'

export default function MagicLinkPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Magic Link Verification
          </h1>
          <p className="text-gray-600">
            Verifying your authentication link
          </p>
        </div>

        <Suspense fallback={
          <div className="bg-white shadow-lg rounded-lg p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        }>
          <MagicLinkVerification />
        </Suspense>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            Need help? Contact our support team.
          </p>
        </div>
      </div>
    </div>
  )
}
