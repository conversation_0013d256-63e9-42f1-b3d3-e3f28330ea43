import { checkRateLimit as checkRateLimitPG } from './postgresql-rate-limit'

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (identifier: string) => string // Custom key generator
}

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  total: number
}

// Default rate limit configurations
export const RATE_LIMITS = {
  // General API rate limiting
  API_GENERAL: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
  },
  
  // Authentication endpoints
  AUTH_MAGIC_LINK: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5,
  },
  
  // Admin endpoints
  ADMIN_API: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200,
  },
  
  // Search endpoints
  SEARCH_API: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50,
  },
  
  // Company creation/updates
  COMPANY_MUTATIONS: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
  },
} as const

export async function checkRateLimit(
  identifier: string,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  return await checkRateLimitPG(identifier, config)
}

// Convenience functions for common rate limits
export async function checkAPIRateLimit(identifier: string): Promise<RateLimitResult> {
  return checkRateLimit(identifier, RATE_LIMITS.API_GENERAL)
}

export async function checkAuthRateLimit(email: string): Promise<RateLimitResult> {
  return checkRateLimit(email.toLowerCase(), {
    ...RATE_LIMITS.AUTH_MAGIC_LINK,
    keyGenerator: (email) => `rate_limit:auth:${email}`
  })
}

export async function checkAdminRateLimit(userId: string): Promise<RateLimitResult> {
  return checkRateLimit(userId, {
    ...RATE_LIMITS.ADMIN_API,
    keyGenerator: (userId) => `rate_limit:admin:${userId}`
  })
}

export async function checkSearchRateLimit(identifier: string): Promise<RateLimitResult> {
  return checkRateLimit(identifier, {
    ...RATE_LIMITS.SEARCH_API,
    keyGenerator: (id) => `rate_limit:search:${id}`
  })
}

export async function checkCompanyMutationRateLimit(userId: string): Promise<RateLimitResult> {
  return checkRateLimit(userId, {
    ...RATE_LIMITS.COMPANY_MUTATIONS,
    keyGenerator: (userId) => `rate_limit:company_mutations:${userId}`
  })
}

// Helper to get client identifier from request
export function getClientIdentifier(request: Request): string {
  // Try to get real IP from headers (for proxied requests)
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  
  // Use the first available IP
  const ip = forwarded?.split(',')[0]?.trim() || 
            realIp || 
            cfConnectingIp || 
            'unknown'
  
  return ip
}

// Helper to add rate limit headers to response
export function addRateLimitHeaders(
  response: Response,
  result: RateLimitResult
): Response {
  const headers = new Headers(response.headers)
  
  headers.set('X-RateLimit-Limit', result.total.toString())
  headers.set('X-RateLimit-Remaining', result.remaining.toString())
  headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString())
  
  if (!result.allowed) {
    headers.set('Retry-After', Math.ceil((result.resetTime - Date.now()) / 1000).toString())
  }
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  })
}
