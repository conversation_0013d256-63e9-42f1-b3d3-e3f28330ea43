-- Migration: Add missing company reports table
-- This migration adds a table to track user reports of missing companies

-- Create missing company reports table
CREATE TABLE missing_company_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_email VARCHAR(255) NOT NULL,
    email_domain VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    status VARCHAR(50) CHECK (status IN ('pending', 'reviewed', 'added', 'rejected')) DEFAULT 'pending',
    admin_notes TEXT,
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL, -- Set when company is added
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_missing_company_reports_user_email ON missing_company_reports(user_email);
CREATE INDEX idx_missing_company_reports_email_domain ON missing_company_reports(email_domain);
CREATE INDEX idx_missing_company_reports_status ON missing_company_reports(status);
CREATE INDEX idx_missing_company_reports_created_at ON missing_company_reports(created_at);

-- Add trigger for updated_at
CREATE TRIGGER update_missing_company_reports_updated_at 
    BEFORE UPDATE ON missing_company_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comment to table
COMMENT ON TABLE missing_company_reports IS 'Tracks user reports of missing companies that should be added to the platform';
COMMENT ON COLUMN missing_company_reports.status IS 'pending: newly reported, reviewed: admin has seen it, added: company was added, rejected: report was rejected';
COMMENT ON COLUMN missing_company_reports.company_id IS 'Set when the reported company is actually added to the platform';
