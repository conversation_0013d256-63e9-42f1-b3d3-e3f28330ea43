import { NextRequest, NextResponse } from 'next/server'
import { verifyCompanyAssociationToken } from '@/lib/email-change-company-association'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token } = body

    console.log('🏢 Company verification attempt:', { token: token?.substring(0, 8) + '...' })

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      )
    }

    // Verify the company association token
    const result = await verifyCompanyAssociationToken(token)

    if (!result.success) {
      console.log('❌ Company verification failed:', result.error)
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    console.log('✅ Company verification successful:', {
      userId: result.userId,
      companyName: result.companyName
    })

    return NextResponse.json({
      success: true,
      message: result.message,
      company: {
        id: result.companyId,
        name: result.companyName
      }
    })

  } catch (error) {
    console.error('Error verifying company association:', error)
    return NextResponse.json(
      { error: 'Failed to verify company association' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      )
    }

    // Verify the company association token
    const result = await verifyCompanyAssociationToken(token)

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message,
      company: {
        id: result.companyId,
        name: result.companyName
      }
    })

  } catch (error) {
    console.error('Error verifying company association:', error)
    return NextResponse.json(
      { error: 'Failed to verify company association' },
      { status: 500 }
    )
  }
}
