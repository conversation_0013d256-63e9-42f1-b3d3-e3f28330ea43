/**
 * Admin Benefit Management Flow Tests
 * Tests for admin benefit CRUD operations, benefit categories, and benefit verification management
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock database operations
vi.mock('@/lib/local-db', () => ({
  query: vi.fn(),
}))

const mockQuery = (await import('@/lib/local-db')).query as unknown as ReturnType<typeof vi.fn>

describe('Admin Benefit Management Flows', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(mockFetch).mockClear()
    mockQuery.mockReset()
  })

  describe('Benefit Creation', () => {
    it('should create a new benefit', async () => {
      const benefitData = {
        name: 'Mental Health Support',
        description: 'Comprehensive mental health and wellness programs',
        category: 'Health',
        icon: '🧠',
        isActive: true
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          benefit: {
            id: 'benefit-123',
            ...benefitData,
            createdAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/benefits', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(benefitData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.benefit.name).toBe('Mental Health Support')
      expect(result.benefit.category).toBe('Health')
      expect(result.benefit.isActive).toBe(true)
    })

    it('should validate required fields for benefit creation', async () => {
      const invalidBenefitData = {
        name: '',
        category: 'Invalid Category'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ 
          error: 'Benefit name is required' 
        })
      })

      const response = await fetch('/api/admin/benefits', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(invalidBenefitData)
      })

      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.error).toBe('Benefit name is required')
    })

    it('should handle duplicate benefit creation', async () => {
      const benefitData = {
        name: 'Health Insurance',
        category: 'Health'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ 
          error: 'Benefit with this name already exists' 
        })
      })

      const response = await fetch('/api/admin/benefits', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(benefitData)
      })

      const result = await response.json()

      expect(response.status).toBe(409)
      expect(result.error).toBe('Benefit with this name already exists')
    })
  })

  describe('Benefit Updates', () => {
    it('should update benefit information', async () => {
      const updateData = {
        name: 'Updated Mental Health Support',
        description: 'Enhanced mental health programs with 24/7 support',
        icon: '🧠💚'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          benefit: {
            id: 'benefit-123',
            ...updateData,
            updatedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/benefits/benefit-123', {
        method: 'PATCH',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(updateData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.benefit.name).toBe('Updated Mental Health Support')
      expect(result.benefit.icon).toBe('🧠💚')
    })

    it('should activate/deactivate benefit', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          benefit: {
            id: 'benefit-123',
            isActive: false,
            deactivatedAt: new Date().toISOString(),
            deactivatedBy: 'admin-123'
          }
        })
      })

      const response = await fetch('/api/admin/benefits/benefit-123/deactivate', {
        method: 'POST',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.benefit.isActive).toBe(false)
      expect(result.benefit.deactivatedBy).toBe('admin-123')
    })

    it('should handle non-existent benefit update', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ 
          error: 'Benefit not found' 
        })
      })

      const response = await fetch('/api/admin/benefits/nonexistent', {
        method: 'PATCH',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ name: 'Updated Name' })
      })

      const result = await response.json()

      expect(response.status).toBe(404)
      expect(result.error).toBe('Benefit not found')
    })
  })

  describe('Benefit Deletion', () => {
    it('should delete benefit', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Benefit deleted successfully'
        })
      })

      const response = await fetch('/api/admin/benefits/benefit-123', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Benefit deleted successfully')
    })

    it('should handle deletion of benefit with dependencies', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ 
          error: 'Cannot delete benefit with existing company associations',
          dependencies: {
            companies: 25,
            userRankings: 150
          }
        })
      })

      const response = await fetch('/api/admin/benefits/benefit-123', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(409)
      expect(result.error).toContain('Cannot delete benefit')
      expect(result.dependencies.companies).toBe(25)
    })

    it('should force delete benefit with dependencies', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Benefit force deleted successfully',
          cleanupActions: [
            'Removed 25 company benefit associations',
            'Removed 150 user ranking entries',
            'Archived 45 benefit verifications'
          ]
        })
      })

      const response = await fetch('/api/admin/benefits/benefit-123?force=true', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.cleanupActions).toHaveLength(3)
    })
  })

  describe('Benefit Category Management', () => {
    it('should create new benefit category', async () => {
      const categoryData = {
        name: 'Wellness',
        description: 'Physical and mental wellness benefits',
        icon: '🌱',
        color: '#4CAF50'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          category: {
            id: 'category-123',
            ...categoryData,
            createdAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/benefit-categories', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(categoryData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.category.name).toBe('Wellness')
      expect(result.category.color).toBe('#4CAF50')
    })

    it('should update benefit category', async () => {
      const updateData = {
        name: 'Health & Wellness',
        description: 'Comprehensive health and wellness programs',
        color: '#2E7D32'
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          category: {
            id: 'category-123',
            ...updateData,
            updatedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/benefit-categories/category-123', {
        method: 'PATCH',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(updateData)
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.category.name).toBe('Health & Wellness')
      expect(result.category.color).toBe('#2E7D32')
    })

    it('should delete benefit category', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          message: 'Benefit category deleted successfully'
        })
      })

      const response = await fetch('/api/admin/benefit-categories/category-123', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Benefit category deleted successfully')
    })

    it('should handle deletion of category with benefits', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ 
          error: 'Cannot delete category with existing benefits',
          benefitCount: 12
        })
      })

      const response = await fetch('/api/admin/benefit-categories/category-123', {
        method: 'DELETE',
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(response.status).toBe(409)
      expect(result.error).toContain('Cannot delete category')
      expect(result.benefitCount).toBe(12)
    })
  })

  describe('Benefit Verification Management', () => {
    it('should get pending benefit verifications', async () => {
      const mockVerifications = [
        {
          id: 'verification-1',
          companyBenefit: {
            id: 'cb-1',
            company: { name: 'Tech Corp' },
            benefit: { name: 'Health Insurance' }
          },
          user: { email: '<EMAIL>' },
          status: 'disputed',
          comment: 'This benefit is no longer offered',
          submittedAt: '2024-01-01T12:00:00Z'
        },
        {
          id: 'verification-2',
          companyBenefit: {
            id: 'cb-2',
            company: { name: 'Design Studio' },
            benefit: { name: 'Remote Work' }
          },
          user: { email: '<EMAIL>' },
          status: 'confirmed',
          comment: 'Yes, we offer full remote work',
          submittedAt: '2024-01-01T11:00:00Z'
        }
      ]

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          verifications: mockVerifications,
          total: 2,
          pending: 1,
          confirmed: 1
        })
      })

      const response = await fetch('/api/admin/benefit-verifications?status=pending', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.verifications).toHaveLength(2)
      expect(result.pending).toBe(1)
      expect(result.confirmed).toBe(1)
    })

    it('should approve benefit verification', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          verification: {
            id: 'verification-1',
            status: 'admin_approved',
            adminComment: 'Verification approved after review',
            approvedBy: 'admin-123',
            approvedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/benefit-verifications/verification-1/approve', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ 
          adminComment: 'Verification approved after review' 
        })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.verification.status).toBe('admin_approved')
      expect(result.verification.approvedBy).toBe('admin-123')
    })

    it('should reject benefit verification', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          verification: {
            id: 'verification-1',
            status: 'admin_rejected',
            adminComment: 'Insufficient evidence provided',
            rejectedBy: 'admin-123',
            rejectedAt: new Date().toISOString()
          }
        })
      })

      const response = await fetch('/api/admin/benefit-verifications/verification-1/reject', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ 
          adminComment: 'Insufficient evidence provided' 
        })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.verification.status).toBe('admin_rejected')
      expect(result.verification.rejectedBy).toBe('admin-123')
    })
  })

  describe('Benefit Analytics and Reporting', () => {
    it('should get benefit analytics dashboard', async () => {
      const mockAnalytics = {
        totalBenefits: 75,
        activeBenefits: 68,
        inactiveBenefits: 7,
        totalCategories: 8,
        verificationStats: {
          pending: 15,
          confirmed: 120,
          disputed: 8,
          adminApproved: 95
        },
        topBenefits: [
          { name: 'Health Insurance', companyCount: 45, userRankings: 200 },
          { name: 'Remote Work', companyCount: 38, userRankings: 180 }
        ],
        recentActivity: [
          {
            type: 'benefit_created',
            benefitName: 'Bike Leasing',
            timestamp: '2024-01-01T12:00:00Z'
          }
        ]
      }

      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => mockAnalytics
      })

      const response = await fetch('/api/admin/benefits/analytics', {
        headers: { 
          'Authorization': 'Bearer admin-token'
        }
      })

      const result = await response.json()

      expect(result.totalBenefits).toBe(75)
      expect(result.verificationStats.pending).toBe(15)
      expect(result.topBenefits).toHaveLength(2)
      expect(result.recentActivity).toHaveLength(1)
    })

    it('should export benefit data', async () => {
      ;(mockFetch).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          exportUrl: '/api/admin/exports/benefits-2024-01-01.csv',
          recordCount: 75
        })
      })

      const response = await fetch('/api/admin/benefits/export', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ format: 'csv', includeVerifications: true })
      })

      const result = await response.json()

      expect(result.success).toBe(true)
      expect(result.exportUrl).toContain('.csv')
      expect(result.recordCount).toBe(75)
    })
  })
})
