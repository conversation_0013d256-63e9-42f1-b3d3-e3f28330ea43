-- Migration: Add company_id field to users table
-- This migration adds explicit company association to users instead of relying solely on email domain matching
-- Date: 2025-01-30

-- Add company_id column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES companies(id) ON DELETE SET NULL;

-- Add index for better query performance
CREATE INDEX IF NOT EXISTS idx_users_company_id ON users(company_id);

-- Populate company_id for existing users based on email domain matching
-- This ensures backward compatibility by setting company_id for users whose email domains match existing companies
UPDATE users 
SET company_id = c.id
FROM companies c
WHERE users.company_id IS NULL 
  AND users.email LIKE '%@' || c.domain
  AND c.domain IS NOT NULL 
  AND c.domain != '';

-- Add comment for documentation
COMMENT ON COLUMN users.company_id IS 'Explicit company association for user - takes precedence over email domain matching';

-- Create company verification tokens table for email change verification
CREATE TABLE IF NOT EXISTS company_verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_email VARCHAR(255) NOT NULL,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for company verification tokens
CREATE INDEX IF NOT EXISTS idx_company_verification_tokens_token ON company_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_company_verification_tokens_user_id ON company_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_company_verification_tokens_company_id ON company_verification_tokens(company_id);
CREATE INDEX IF NOT EXISTS idx_company_verification_tokens_expires_at ON company_verification_tokens(expires_at);

-- Add to migration log
INSERT INTO migration_log (migration_name, description, applied_at) 
VALUES (
  '005-add-company-id-to-users', 
  'Added explicit company_id field to users table for better company association tracking',
  NOW()
) ON CONFLICT (migration_name) DO NOTHING;

-- Create migration_log table if it doesn't exist (in case previous migration didn't run)
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
