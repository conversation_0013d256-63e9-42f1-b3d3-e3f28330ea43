import { test, expect } from '@playwright/test'
import { signInAdmin, clearAuth } from './auth-helpers'

test.describe('Admin API E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await clearAuth(page)
    // Add aggressive delay between tests to prevent rate limiting
    await page.waitForTimeout(25000)
  })

  test('Admin API Access and Functionality', async ({ page }) => {
    // 1. Sign in as admin
    await signInAdmin(page)
    console.log('After sign in, current URL:', page.url())

    // 2. Verify admin authentication
    const userResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/auth/me')
        return {
          status: response.status,
          data: response.ok ? await response.json() : await response.text()
        }
      } catch (error) {
        return { error: error.message }
      }
    })
    
    expect(userResponse.status).toBe(200)
    expect(userResponse.data.user.role).toBe('admin')
    console.log('✅ Admin authentication verified')

    // 3. Test admin companies API
    const companiesResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/admin/companies')
        return {
          status: response.status,
          data: response.ok ? await response.json() : await response.text()
        }
      } catch (error) {
        return { error: error.message }
      }
    })

    expect(companiesResponse.status).toBe(200)
    expect(companiesResponse.data.companies).toBeDefined()
    expect(Array.isArray(companiesResponse.data.companies)).toBe(true)
    expect(companiesResponse.data.companies.length).toBeGreaterThan(0)
    console.log('✅ Admin companies API working:', companiesResponse.data.companies.length, 'companies')

    // 4. Verify test companies are in the response
    const companyNames = companiesResponse.data.companies.map((c: any) => c.name)
    expect(companyNames).toContain('E2E Tech Corp')
    expect(companyNames).toContain('E2E Industries')
    expect(companyNames).toContain('E2E Startup')
    console.log('✅ Test companies found in API response')

    // 5. Test admin users API
    const usersResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/admin/users')
        return {
          status: response.status,
          data: response.ok ? await response.json() : await response.text()
        }
      } catch (error) {
        return { error: error.message }
      }
    })

    expect(usersResponse.status).toBe(200)
    expect(usersResponse.data.users).toBeDefined()
    expect(Array.isArray(usersResponse.data.users)).toBe(true)
    console.log('✅ Admin users API working:', usersResponse.data.users.length, 'users')

    // 6. Test admin benefits API
    const benefitsResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/admin/benefits')
        return {
          status: response.status,
          data: response.ok ? await response.json() : await response.text()
        }
      } catch (error) {
        return { error: error.message }
      }
    })

    expect(benefitsResponse.status).toBe(200)
    expect(benefitsResponse.data.benefits).toBeDefined()
    expect(Array.isArray(benefitsResponse.data.benefits)).toBe(true)
    console.log('✅ Admin benefits API working:', benefitsResponse.data.benefits.length, 'benefits')

    // 7. All core admin APIs verified successfully
    console.log('✅ All core admin APIs verified: companies, users, benefits')

    console.log('🎉 All admin API functionality verified successfully!')
  })

  test('Admin Page Loads Successfully', async ({ page }) => {
    // 1. Sign in as admin (create fresh token)
    await signInAdmin(page, '<EMAIL>')

    // 2. Navigate to admin page
    await page.goto('/admin')
    await page.waitForTimeout(3000) // Wait for page to stabilize

    // 3. Verify admin page loads
    await expect(page.locator('text=Platform Administration').first()).toBeVisible({ timeout: 15000 })
    console.log('✅ Admin page loads successfully')

    // 4. Verify URL stays on admin page
    expect(page.url()).toContain('/admin')
    console.log('✅ Admin page URL is stable')

    console.log('🎉 Admin page functionality verified successfully!')
  })
})
