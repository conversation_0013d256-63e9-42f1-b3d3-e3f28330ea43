-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Functions for cache management
CREATE FUNCTION cleanup_expired_cache() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Also cleanup expired CSRF tokens
    DELETE FROM csrf_tokens WHERE expires_at < NOW();

    -- Cleanup expired rate limits
    DELETE FROM rate_limits WHERE expires_at < NOW();

    RETURN deleted_count;
END;
$$;

CREATE FUNCTION cleanup_expired_magic_links() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$;

CREATE FUNCTION cleanup_expired_sessions() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions
    WHERE expires_at < NOW();

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Log the cleanup operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        metadata,
        created_at
    ) VALUES (
        'session_cleanup',
        'Automated cleanup of expired sessions',
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );

    RETURN deleted_count;
END;
$$;

CREATE FUNCTION clear_cache_pattern(pattern character varying) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key LIKE pattern;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

CREATE FUNCTION delete_all_user_sessions(target_user_id uuid) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions
    WHERE user_id = target_user_id;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Log the operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        user_id,
        metadata,
        created_at
    ) VALUES (
        'user_sessions_deleted',
        'All sessions deleted for user',
        target_user_id::VARCHAR,
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );

    RETURN deleted_count;
END;
$$;

CREATE FUNCTION delete_cache(key character varying) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key = key;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count > 0;
END;
$$;

CREATE FUNCTION get_active_session_stats() RETURNS TABLE(total_sessions integer, expired_sessions integer, active_sessions integer, oldest_session timestamp with time zone, newest_session timestamp with time zone)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_sessions,
        COUNT(*) FILTER (WHERE expires_at < NOW())::INTEGER as expired_sessions,
        COUNT(*) FILTER (WHERE expires_at >= NOW())::INTEGER as active_sessions,
        MIN(created_at) as oldest_session,
        MAX(created_at) as newest_session
    FROM user_sessions;
END;
$$;

CREATE FUNCTION get_cache(key character varying) RETURNS jsonb
    LANGUAGE plpgsql
    AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT cache_value INTO result
    FROM cache_store
    WHERE cache_key = key AND expires_at > NOW();

    RETURN result;
END;
$$;

CREATE FUNCTION get_csrf_token(session_id character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
DECLARE
    result VARCHAR;
BEGIN
    SELECT token INTO result
    FROM csrf_tokens
    WHERE session_id = session_id AND expires_at > NOW();

    RETURN result;
END;
$$;

CREATE FUNCTION get_session_config(config_name character varying) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    config_value TEXT;
BEGIN
    SELECT setting_value INTO config_value
    FROM session_config
    WHERE setting_name = config_name;

    RETURN config_value;
END;
$$;

CREATE FUNCTION log_session_activity(token character varying, activity character varying, ip inet DEFAULT NULL::inet, agent text DEFAULT NULL::text) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO session_activity (session_token, activity_type, ip_address, user_agent)
    VALUES (token, activity, ip, agent);
END;
$$;

CREATE FUNCTION refresh_cache_views() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Refresh materialized views
    REFRESH MATERIALIZED VIEW CONCURRENTLY companies_with_benefits_cache;
    REFRESH MATERIALIZED VIEW CONCURRENTLY benefits_with_categories_cache;

    -- Log the refresh
    INSERT INTO activity_log (
        event_type,
        event_description,
        created_at
    ) VALUES (
        'cache_refresh',
        'Materialized views refreshed',
        NOW()
    );
END;
$$;

CREATE FUNCTION set_cache(key character varying, value jsonb, ttl_seconds integer DEFAULT 3600) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO cache_store (cache_key, cache_value, expires_at)
    VALUES (key, value, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (cache_key)
    DO UPDATE SET
        cache_value = EXCLUDED.cache_value,
        expires_at = EXCLUDED.expires_at,
        updated_at = NOW();

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;

CREATE FUNCTION set_csrf_token(session_id character varying, token character varying, ttl_seconds integer DEFAULT 3600) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO csrf_tokens (session_id, token, expires_at)
    VALUES (session_id, token, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (session_id)
    DO UPDATE SET
        token = EXCLUDED.token,
        expires_at = EXCLUDED.expires_at,
        created_at = NOW();

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;

CREATE FUNCTION update_company_analytics_summary(target_company_id uuid, target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO company_analytics_summary (
        company_id,
        date,
        page_views,
        unique_visitors,
        benefit_interactions,
        search_appearances
    )
    SELECT
        target_company_id,
        target_date,
        COALESCE(views.page_views, 0),
        COALESCE(views.unique_visitors, 0),
        COALESCE(interactions.benefit_interactions, 0),
        COALESCE(appearances.search_appearances, 0)
    FROM (
        SELECT
            COUNT(*) as page_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) views
    CROSS JOIN (
        SELECT
            COUNT(*) as benefit_interactions
        FROM benefit_search_interactions
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        SELECT
            COUNT(DISTINCT sq.id) as search_appearances
        FROM search_queries sq
        JOIN benefit_search_interactions bsi ON sq.id = bsi.search_query_id
        WHERE bsi.company_id = target_company_id
        AND DATE(sq.created_at) = target_date
    ) appearances
    ON CONFLICT (company_id, date) DO UPDATE SET
        page_views = EXCLUDED.page_views,
        unique_visitors = EXCLUDED.unique_visitors,
        benefit_interactions = EXCLUDED.benefit_interactions,
        search_appearances = EXCLUDED.search_appearances,
        updated_at = NOW();
END;
$$;

CREATE FUNCTION update_daily_analytics_summary(target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO daily_analytics_summary (
        date,
        total_company_views,
        total_searches,
        total_benefit_interactions,
        unique_visitors,
        unique_searchers,
        top_searched_benefits,
        top_viewed_companies
    )
    SELECT
        target_date,
        COALESCE(company_views.total_views, 0),
        COALESCE(searches.total_searches, 0),
        COALESCE(interactions.total_interactions, 0),
        COALESCE(company_views.unique_visitors, 0),
        COALESCE(searches.unique_searchers, 0),
        COALESCE(top_benefits.benefits, '[]'::jsonb),
        COALESCE(top_companies.companies, '[]'::jsonb)
    FROM (
        -- Company views for the day
        SELECT
            COUNT(*) as total_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE DATE(created_at) = target_date
    ) company_views
    CROSS JOIN (
        -- Searches for the day
        SELECT
            COUNT(*) as total_searches,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_searchers
        FROM search_queries
        WHERE DATE(created_at) = target_date
    ) searches
    CROSS JOIN (
        -- Benefit interactions for the day
        SELECT
            COUNT(*) as total_interactions
        FROM benefit_search_interactions
        WHERE DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        -- Top searched benefits
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'benefit_id', b.id,
                    'benefit_name', b.name,
                    'search_count', benefit_searches.search_count
                )
                ORDER BY benefit_searches.search_count DESC
            ) FILTER (WHERE benefit_searches.search_count > 0), '[]'::jsonb) as benefits
        FROM (
            SELECT
                bsi.benefit_id,
                COUNT(*) as search_count
            FROM benefit_search_interactions bsi
            JOIN search_queries sq ON bsi.search_query_id = sq.id
            WHERE DATE(sq.created_at) = target_date
            GROUP BY bsi.benefit_id
            ORDER BY search_count DESC
            LIMIT 10
        ) benefit_searches
        JOIN benefits b ON benefit_searches.benefit_id = b.id
    ) top_benefits
    CROSS JOIN (
        -- Top viewed companies
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'company_id', c.id,
                    'company_name', c.name,
                    'view_count', company_views.view_count
                )
                ORDER BY company_views.view_count DESC
            ) FILTER (WHERE company_views.view_count > 0), '[]'::jsonb) as companies
        FROM (
            SELECT
                cpv.company_id,
                COUNT(*) as view_count
            FROM company_page_views cpv
            WHERE DATE(cpv.created_at) = target_date
            GROUP BY cpv.company_id
            ORDER BY view_count DESC
            LIMIT 10
        ) company_views
        JOIN companies c ON company_views.company_id = c.id
    ) top_companies
    ON CONFLICT (date) DO UPDATE SET
        total_company_views = EXCLUDED.total_company_views,
        total_searches = EXCLUDED.total_searches,
        total_benefit_interactions = EXCLUDED.total_benefit_interactions,
        unique_visitors = EXCLUDED.unique_visitors,
        unique_searchers = EXCLUDED.unique_searchers,
        top_searched_benefits = EXCLUDED.top_searched_benefits,
        top_viewed_companies = EXCLUDED.top_viewed_companies,
        updated_at = NOW();
END;
$$;

CREATE FUNCTION update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Tables
CREATE TABLE activity_log (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    event_type character varying(50) NOT NULL,
    event_description text NOT NULL,
    user_id character varying(255),
    user_email character varying(255),
    user_name character varying(255),
    company_id uuid,
    company_name character varying(255),
    benefit_id uuid,
    benefit_name character varying(255),
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT activity_log_event_type_check CHECK (((event_type)::text = ANY (ARRAY[('company_added'::character varying)::text, ('user_registered'::character varying)::text, ('benefit_verified'::character varying)::text, ('benefit_disputed'::character varying)::text, ('benefit_removal_dispute_submitted'::character varying)::text, ('benefit_removal_dispute_approved'::character varying)::text, ('benefit_removal_dispute_rejected'::character varying)::text, ('benefit_removal_dispute_cancelled'::character varying)::text, ('benefit_automatically_removed'::character varying)::text, ('session_cleanup'::character varying)::text, ('cache_refresh'::character varying)::text, ('cache_maintenance'::character varying)::text, ('user_login'::character varying)::text, ('user_logout'::character varying)::text, ('company_verified'::character varying)::text, ('company_rejected'::character varying)::text])))
);

CREATE TABLE benefit_categories (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(255) NOT NULL,
    description text,
    icon character varying(50),
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE benefit_removal_disputes (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id uuid NOT NULL,
    reason text NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_user_id uuid,
    admin_comment text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_removal_disputes_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('approved'::character varying)::text, ('rejected'::character varying)::text, ('cancelled'::character varying)::text])))
);

CREATE TABLE benefit_search_interactions (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    search_query_id uuid,
    benefit_id uuid NOT NULL,
    company_id uuid NOT NULL,
    interaction_type character varying(50) NOT NULL,
    user_id uuid,
    session_id character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_search_interactions_interaction_type_check CHECK (((interaction_type)::text = ANY (ARRAY[('view'::character varying)::text, ('click'::character varying)::text, ('verify'::character varying)::text, ('dispute'::character varying)::text])))
);

CREATE TABLE benefit_verifications (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id character varying(255) NOT NULL,
    status character varying(50) NOT NULL,
    comment text,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_verifications_status_check CHECK (((status)::text = ANY (ARRAY[('confirmed'::character varying)::text, ('disputed'::character varying)::text])))
);

CREATE TABLE benefits (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    icon character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    description text,
    category_id uuid NOT NULL
);

CREATE TABLE companies (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    size character varying(50) NOT NULL,
    industry character varying(255) NOT NULL,
    description text,
    domain character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    career_url character varying(500),
    website character varying(500),
    logo_url character varying(500),
    founded_year integer,
    CONSTRAINT companies_size_check CHECK (((size)::text = ANY (ARRAY[('startup'::character varying)::text, ('small'::character varying)::text, ('medium'::character varying)::text, ('large'::character varying)::text, ('enterprise'::character varying)::text])))
);

CREATE TABLE company_benefits (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    added_by character varying(255),
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE cache_store (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    cache_key character varying(255) NOT NULL,
    cache_value jsonb NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE company_locations (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    location_raw character varying(255) NOT NULL,
    location_normalized character varying(255) NOT NULL,
    city character varying(100),
    country character varying(100),
    country_code character varying(2),
    latitude numeric(10,8),
    longitude numeric(11,8),
    is_primary boolean DEFAULT false,
    is_headquarters boolean DEFAULT false,
    location_type character varying(50) DEFAULT 'office'::character varying,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT company_locations_location_type_check CHECK (((location_type)::text = ANY (ARRAY[('office'::character varying)::text, ('headquarters'::character varying)::text, ('branch'::character varying)::text, ('remote'::character varying)::text])))
);

CREATE TABLE company_analytics_summary (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    date date NOT NULL,
    page_views integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    benefit_interactions integer DEFAULT 0,
    search_appearances integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE company_page_views (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    user_id uuid,
    session_id character varying(255),
    ip_address inet,
    user_agent text,
    referrer text,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE company_users (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    email character varying(255) NOT NULL,
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE company_verification_tokens (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    user_email character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE csrf_tokens (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    session_id character varying(255) NOT NULL,
    token character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE daily_analytics_summary (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    date date NOT NULL,
    total_company_views integer DEFAULT 0,
    total_searches integer DEFAULT 0,
    total_benefit_interactions integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    unique_searchers integer DEFAULT 0,
    top_searched_benefits jsonb,
    top_viewed_companies jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE magic_link_rate_limits (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    request_count integer DEFAULT 1,
    window_start timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE magic_link_tokens (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    user_data jsonb,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE migration_log (
    id integer NOT NULL,
    migration_name character varying(255) NOT NULL,
    description text,
    applied_at timestamp with time zone DEFAULT now()
);

CREATE SEQUENCE migration_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE migration_log_id_seq OWNED BY migration_log.id;

CREATE TABLE missing_company_reports (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    user_email character varying(255) NOT NULL,
    email_domain character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_notes text,
    company_id uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT missing_company_reports_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('reviewed'::character varying)::text, ('added'::character varying)::text, ('rejected'::character varying)::text])))
);

CREATE TABLE rate_limits (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    identifier character varying(255) NOT NULL,
    window_start timestamp with time zone NOT NULL,
    request_count integer DEFAULT 1,
    request_timestamps jsonb DEFAULT '[]'::jsonb,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE saved_companies (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    user_id character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE search_queries (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    query_text text NOT NULL,
    user_id uuid,
    session_id character varying(255),
    results_count integer DEFAULT 0,
    filters_applied jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE session_activity (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    session_token character varying(255) NOT NULL,
    activity_type character varying(50) NOT NULL,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE session_config (
    id integer NOT NULL,
    setting_name character varying(100) NOT NULL,
    setting_value text NOT NULL,
    description text,
    updated_at timestamp with time zone DEFAULT now()
);

CREATE SEQUENCE session_config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE session_config_id_seq OWNED BY session_config.id;

CREATE TABLE user_sessions (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    session_token character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE users (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    email_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    role character varying(50) DEFAULT 'user'::character varying,
    payment_status character varying(50) DEFAULT 'free'::character varying,
    company_id uuid,
    CONSTRAINT users_payment_status_check CHECK (((payment_status)::text = ANY (ARRAY[('free'::character varying)::text, ('paying'::character varying)::text]))),
    CONSTRAINT users_role_check CHECK (((role)::text = ANY (ARRAY[('user'::character varying)::text, ('admin'::character varying)::text])))
);

CREATE TABLE user_benefit_rankings (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    ranking integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT user_benefit_rankings_ranking_check CHECK (((ranking >= 1) AND (ranking <= 10)))
);

-- Set default values for sequences
ALTER TABLE ONLY migration_log ALTER COLUMN id SET DEFAULT nextval('migration_log_id_seq'::regclass);
ALTER TABLE ONLY session_config ALTER COLUMN id SET DEFAULT nextval('session_config_id_seq'::regclass);

-- Materialized Views
CREATE MATERIALIZED VIEW benefits_with_categories_cache AS
 SELECT b.id,
    b.name,
    b.category_id,
    b.icon,
    b.description,
    b.created_at,
    bc.name AS category_name,
    bc.display_name AS category_display_name,
    bc.icon AS category_icon,
    bc.description AS category_description,
    count(cb.company_id) AS company_count
   FROM (((benefits b
     LEFT JOIN benefit_categories bc ON ((b.category_id = bc.id)))
     LEFT JOIN company_benefits cb ON ((b.id = cb.benefit_id)))
     LEFT JOIN companies c ON ((cb.company_id = c.id)))
  GROUP BY b.id, b.name, b.category_id, b.icon, b.description, b.created_at, bc.name, bc.display_name, bc.icon, bc.description
  ORDER BY b.name
  WITH NO DATA;

CREATE MATERIALIZED VIEW companies_with_benefits_cache AS
 SELECT c.id,
    c.name,
    c.description,
    c.website,
    c.logo_url,
    c.size,
    c.industry,
    c.founded_year,
    c.created_at,
    c.updated_at,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', b.id, 'name', b.name, 'category_id', b.category_id, 'icon', b.icon, 'description', b.description, 'category_name', bc.name, 'category_display_name', bc.display_name, 'category_icon', bc.icon)) FILTER (WHERE (b.id IS NOT NULL)), '[]'::json) AS benefits,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', cl.id, 'location_type', cl.location_type, 'city', cl.city, 'country', cl.country, 'country_code', cl.country_code, 'is_primary', cl.is_primary, 'is_headquarters', cl.is_headquarters, 'location_raw', cl.location_raw, 'location_normalized', cl.location_normalized)) FILTER (WHERE (cl.id IS NOT NULL)), '[]'::json) AS locations
   FROM ((((companies c
     LEFT JOIN company_benefits cb ON ((c.id = cb.company_id)))
     LEFT JOIN benefits b ON ((cb.benefit_id = b.id)))
     LEFT JOIN benefit_categories bc ON ((b.category_id = bc.id)))
     LEFT JOIN company_locations cl ON ((c.id = cl.company_id)))
  GROUP BY c.id, c.name, c.description, c.website, c.logo_url, c.size, c.industry, c.founded_year, c.created_at, c.updated_at
  WITH NO DATA;

-- Session monitoring view
CREATE VIEW session_monitoring AS
 SELECT u.email,
    u.first_name,
    u.last_name,
    us.session_token,
    us.created_at,
    us.expires_at,
        CASE
            WHEN (us.expires_at < now()) THEN 'expired'::text
            ELSE 'active'::text
        END AS status,
    (EXTRACT(epoch FROM (us.expires_at - now())) / (3600)::numeric) AS hours_until_expiry
   FROM (user_sessions us
     JOIN users u ON ((us.user_id = u.id)))
  ORDER BY us.created_at DESC;

-- Primary Key Constraints
ALTER TABLE ONLY activity_log ADD CONSTRAINT activity_log_pkey PRIMARY KEY (id);
ALTER TABLE ONLY benefit_categories ADD CONSTRAINT benefit_categories_pkey PRIMARY KEY (id);
ALTER TABLE ONLY benefit_removal_disputes ADD CONSTRAINT benefit_removal_disputes_pkey PRIMARY KEY (id);
ALTER TABLE ONLY benefit_search_interactions ADD CONSTRAINT benefit_search_interactions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY benefit_verifications ADD CONSTRAINT benefit_verifications_pkey PRIMARY KEY (id);
ALTER TABLE ONLY benefits ADD CONSTRAINT benefits_pkey PRIMARY KEY (id);
ALTER TABLE ONLY cache_store ADD CONSTRAINT cache_store_pkey PRIMARY KEY (id);
ALTER TABLE ONLY companies ADD CONSTRAINT companies_pkey PRIMARY KEY (id);
ALTER TABLE ONLY company_analytics_summary ADD CONSTRAINT company_analytics_summary_pkey PRIMARY KEY (id);
ALTER TABLE ONLY company_benefits ADD CONSTRAINT company_benefits_pkey PRIMARY KEY (id);
ALTER TABLE ONLY company_locations ADD CONSTRAINT company_locations_pkey PRIMARY KEY (id);
ALTER TABLE ONLY company_page_views ADD CONSTRAINT company_page_views_pkey PRIMARY KEY (id);
ALTER TABLE ONLY company_users ADD CONSTRAINT company_users_pkey PRIMARY KEY (id);
ALTER TABLE ONLY company_verification_tokens ADD CONSTRAINT company_verification_tokens_pkey PRIMARY KEY (id);
ALTER TABLE ONLY csrf_tokens ADD CONSTRAINT csrf_tokens_pkey PRIMARY KEY (id);
ALTER TABLE ONLY daily_analytics_summary ADD CONSTRAINT daily_analytics_summary_pkey PRIMARY KEY (id);
ALTER TABLE ONLY magic_link_rate_limits ADD CONSTRAINT magic_link_rate_limits_pkey PRIMARY KEY (id);
ALTER TABLE ONLY magic_link_tokens ADD CONSTRAINT magic_link_tokens_pkey PRIMARY KEY (id);
ALTER TABLE ONLY migration_log ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);
ALTER TABLE ONLY missing_company_reports ADD CONSTRAINT missing_company_reports_pkey PRIMARY KEY (id);
ALTER TABLE ONLY rate_limits ADD CONSTRAINT rate_limits_pkey PRIMARY KEY (id);
ALTER TABLE ONLY saved_companies ADD CONSTRAINT saved_companies_pkey PRIMARY KEY (id);
ALTER TABLE ONLY search_queries ADD CONSTRAINT search_queries_pkey PRIMARY KEY (id);
ALTER TABLE ONLY session_activity ADD CONSTRAINT session_activity_pkey PRIMARY KEY (id);
ALTER TABLE ONLY session_config ADD CONSTRAINT session_config_pkey PRIMARY KEY (id);
ALTER TABLE ONLY user_benefit_rankings ADD CONSTRAINT user_benefit_rankings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY user_sessions ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY users ADD CONSTRAINT users_pkey PRIMARY KEY (id);

-- Unique Constraints
ALTER TABLE ONLY benefit_categories ADD CONSTRAINT benefit_categories_name_key UNIQUE (name);
ALTER TABLE ONLY benefit_removal_disputes ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_user_id_key UNIQUE (company_benefit_id, user_id);
ALTER TABLE ONLY benefits ADD CONSTRAINT benefits_name_key UNIQUE (name);
ALTER TABLE ONLY cache_store ADD CONSTRAINT cache_store_cache_key_key UNIQUE (cache_key);
ALTER TABLE ONLY company_analytics_summary ADD CONSTRAINT company_analytics_summary_company_id_date_key UNIQUE (company_id, date);
ALTER TABLE ONLY company_benefits ADD CONSTRAINT company_benefits_company_id_benefit_id_key UNIQUE (company_id, benefit_id);
ALTER TABLE ONLY company_locations ADD CONSTRAINT company_locations_company_id_location_normalized_key UNIQUE (company_id, location_normalized);
ALTER TABLE ONLY company_users ADD CONSTRAINT company_users_company_id_email_key UNIQUE (company_id, email);
ALTER TABLE ONLY company_verification_tokens ADD CONSTRAINT company_verification_tokens_token_key UNIQUE (token);
ALTER TABLE ONLY csrf_tokens ADD CONSTRAINT csrf_tokens_session_id_key UNIQUE (session_id);
ALTER TABLE ONLY daily_analytics_summary ADD CONSTRAINT daily_analytics_summary_date_key UNIQUE (date);
ALTER TABLE ONLY magic_link_rate_limits ADD CONSTRAINT magic_link_rate_limits_email_key UNIQUE (email);
ALTER TABLE ONLY magic_link_tokens ADD CONSTRAINT magic_link_tokens_token_key UNIQUE (token);
ALTER TABLE ONLY migration_log ADD CONSTRAINT migration_log_migration_name_key UNIQUE (migration_name);
ALTER TABLE ONLY rate_limits ADD CONSTRAINT rate_limits_identifier_window_start_key UNIQUE (identifier, window_start);
ALTER TABLE ONLY saved_companies ADD CONSTRAINT saved_companies_user_id_company_id_key UNIQUE (user_id, company_id);
ALTER TABLE ONLY session_config ADD CONSTRAINT session_config_setting_name_key UNIQUE (setting_name);
ALTER TABLE ONLY user_benefit_rankings ADD CONSTRAINT user_benefit_rankings_user_id_benefit_id_key UNIQUE (user_id, benefit_id);
ALTER TABLE ONLY user_sessions ADD CONSTRAINT user_sessions_session_token_key UNIQUE (session_token);
ALTER TABLE ONLY users ADD CONSTRAINT users_email_key UNIQUE (email);

-- Foreign Key Constraints
ALTER TABLE ONLY benefit_removal_disputes ADD CONSTRAINT benefit_removal_disputes_admin_user_id_fkey FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE ONLY benefit_removal_disputes ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES company_benefits(id) ON DELETE CASCADE;
ALTER TABLE ONLY benefit_removal_disputes ADD CONSTRAINT benefit_removal_disputes_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE ONLY benefit_search_interactions ADD CONSTRAINT benefit_search_interactions_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES benefits(id) ON DELETE CASCADE;
ALTER TABLE ONLY benefit_search_interactions ADD CONSTRAINT benefit_search_interactions_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY benefit_search_interactions ADD CONSTRAINT benefit_search_interactions_search_query_id_fkey FOREIGN KEY (search_query_id) REFERENCES search_queries(id) ON DELETE CASCADE;
ALTER TABLE ONLY benefit_search_interactions ADD CONSTRAINT benefit_search_interactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE ONLY benefit_verifications ADD CONSTRAINT benefit_verifications_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES company_benefits(id) ON DELETE CASCADE;
ALTER TABLE ONLY benefits ADD CONSTRAINT benefits_category_id_fkey FOREIGN KEY (category_id) REFERENCES benefit_categories(id);
ALTER TABLE ONLY company_analytics_summary ADD CONSTRAINT company_analytics_summary_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY company_benefits ADD CONSTRAINT company_benefits_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES benefits(id) ON DELETE CASCADE;
ALTER TABLE ONLY company_benefits ADD CONSTRAINT company_benefits_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY company_locations ADD CONSTRAINT company_locations_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY company_page_views ADD CONSTRAINT company_page_views_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY company_page_views ADD CONSTRAINT company_page_views_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE ONLY company_users ADD CONSTRAINT company_users_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY company_verification_tokens ADD CONSTRAINT company_verification_tokens_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY company_verification_tokens ADD CONSTRAINT company_verification_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE ONLY missing_company_reports ADD CONSTRAINT missing_company_reports_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL;
ALTER TABLE ONLY saved_companies ADD CONSTRAINT saved_companies_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE ONLY search_queries ADD CONSTRAINT search_queries_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;
ALTER TABLE ONLY user_benefit_rankings ADD CONSTRAINT user_benefit_rankings_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES benefits(id) ON DELETE CASCADE;
ALTER TABLE ONLY user_benefit_rankings ADD CONSTRAINT user_benefit_rankings_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE ONLY user_sessions ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE ONLY users ADD CONSTRAINT users_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL;

-- Indexes for better performance
CREATE INDEX idx_activity_log_benefit_id ON activity_log USING btree (benefit_id);
CREATE INDEX idx_activity_log_company_id ON activity_log USING btree (company_id);
CREATE INDEX idx_activity_log_created_at ON activity_log USING btree (created_at DESC);
CREATE INDEX idx_activity_log_event_type ON activity_log USING btree (event_type);
CREATE INDEX idx_activity_log_user_id ON activity_log USING btree (user_id);
CREATE INDEX idx_benefit_categories_is_active ON benefit_categories USING btree (is_active);
CREATE INDEX idx_benefit_categories_name ON benefit_categories USING btree (name);
CREATE INDEX idx_benefit_categories_sort_order ON benefit_categories USING btree (sort_order);
CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON benefit_removal_disputes USING btree (company_benefit_id);
CREATE INDEX idx_benefit_removal_disputes_created_at ON benefit_removal_disputes USING btree (created_at);
CREATE INDEX idx_benefit_removal_disputes_status ON benefit_removal_disputes USING btree (status);
CREATE INDEX idx_benefit_removal_disputes_user_id ON benefit_removal_disputes USING btree (user_id);
CREATE INDEX idx_benefit_search_interactions_benefit_id ON benefit_search_interactions USING btree (benefit_id);
CREATE INDEX idx_benefit_search_interactions_company_id ON benefit_search_interactions USING btree (company_id);
CREATE INDEX idx_benefit_search_interactions_created_at ON benefit_search_interactions USING btree (created_at);
CREATE INDEX idx_benefit_search_interactions_search_query_id ON benefit_search_interactions USING btree (search_query_id);
CREATE INDEX idx_benefit_search_interactions_user_id ON benefit_search_interactions USING btree (user_id);
CREATE INDEX idx_benefit_verifications_company_benefit_id ON benefit_verifications USING btree (company_benefit_id);
CREATE INDEX idx_benefit_verifications_status ON benefit_verifications USING btree (status);
CREATE INDEX idx_benefit_verifications_user_id ON benefit_verifications USING btree (user_id);
CREATE INDEX idx_benefits_category_id ON benefits USING btree (category_id);
CREATE INDEX idx_benefits_name ON benefits USING btree (name);
CREATE INDEX idx_cache_store_cache_key ON cache_store USING btree (cache_key);
CREATE INDEX idx_cache_store_expires_at ON cache_store USING btree (expires_at);
CREATE INDEX idx_companies_domain ON companies USING btree (domain);
CREATE INDEX idx_companies_industry ON companies USING btree (industry);
CREATE INDEX idx_companies_size ON companies USING btree (size);
CREATE INDEX idx_company_analytics_summary_company_id ON company_analytics_summary USING btree (company_id);
CREATE INDEX idx_company_analytics_summary_date ON company_analytics_summary USING btree (date);
CREATE INDEX idx_company_benefits_benefit_id ON company_benefits USING btree (benefit_id);
CREATE INDEX idx_company_benefits_company_id ON company_benefits USING btree (company_id);
CREATE INDEX idx_company_benefits_verified ON company_benefits USING btree (is_verified);
CREATE INDEX idx_company_locations_city ON company_locations USING btree (city);
CREATE INDEX idx_company_locations_company_id ON company_locations USING btree (company_id);
CREATE INDEX idx_company_locations_coords ON company_locations USING btree (latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;
CREATE INDEX idx_company_locations_country ON company_locations USING btree (country);
CREATE INDEX idx_company_locations_country_code ON company_locations USING btree (country_code);
CREATE INDEX idx_company_locations_headquarters ON company_locations USING btree (is_headquarters) WHERE is_headquarters = true;
CREATE INDEX idx_company_locations_normalized ON company_locations USING btree (location_normalized);
CREATE INDEX idx_company_locations_primary ON company_locations USING btree (is_primary) WHERE is_primary = true;
CREATE INDEX idx_company_locations_type ON company_locations USING btree (location_type);
CREATE INDEX idx_company_page_views_company_id ON company_page_views USING btree (company_id);
CREATE INDEX idx_company_page_views_created_at ON company_page_views USING btree (created_at);
CREATE INDEX idx_company_page_views_session_id ON company_page_views USING btree (session_id);
CREATE INDEX idx_company_page_views_user_id ON company_page_views USING btree (user_id);
CREATE INDEX idx_company_users_company_id ON company_users USING btree (company_id);
CREATE INDEX idx_company_users_email ON company_users USING btree (email);
CREATE INDEX idx_company_verification_tokens_company_id ON company_verification_tokens USING btree (company_id);
CREATE INDEX idx_company_verification_tokens_token ON company_verification_tokens USING btree (token);
CREATE INDEX idx_company_verification_tokens_user_id ON company_verification_tokens USING btree (user_id);
CREATE INDEX idx_csrf_tokens_expires_at ON csrf_tokens USING btree (expires_at);
CREATE INDEX idx_csrf_tokens_session_id ON csrf_tokens USING btree (session_id);
CREATE INDEX idx_daily_analytics_summary_date ON daily_analytics_summary USING btree (date);
CREATE INDEX idx_magic_link_rate_limits_email ON magic_link_rate_limits USING btree (email);
CREATE INDEX idx_magic_link_rate_limits_window_start ON magic_link_rate_limits USING btree (window_start);
CREATE INDEX idx_magic_link_tokens_email ON magic_link_tokens USING btree (email);
CREATE INDEX idx_magic_link_tokens_token ON magic_link_tokens USING btree (token);
CREATE INDEX idx_missing_company_reports_email_domain ON missing_company_reports USING btree (email_domain);
CREATE INDEX idx_missing_company_reports_status ON missing_company_reports USING btree (status);
CREATE INDEX idx_rate_limits_expires_at ON rate_limits USING btree (expires_at);
CREATE INDEX idx_rate_limits_identifier ON rate_limits USING btree (identifier);
CREATE INDEX idx_saved_companies_company_id ON saved_companies USING btree (company_id);
CREATE INDEX idx_saved_companies_user_id ON saved_companies USING btree (user_id);
CREATE INDEX idx_search_queries_created_at ON search_queries USING btree (created_at);
CREATE INDEX idx_search_queries_query_text ON search_queries USING gin(to_tsvector('english', query_text));
CREATE INDEX idx_search_queries_session_id ON search_queries USING btree (session_id);
CREATE INDEX idx_search_queries_user_id ON search_queries USING btree (user_id);
CREATE INDEX idx_session_activity_activity_type ON session_activity USING btree (activity_type);
CREATE INDEX idx_session_activity_created_at ON session_activity USING btree (created_at);
CREATE INDEX idx_session_activity_session_token ON session_activity USING btree (session_token);
CREATE INDEX idx_user_benefit_rankings_benefit_id ON user_benefit_rankings USING btree (benefit_id);
CREATE INDEX idx_user_benefit_rankings_ranking ON user_benefit_rankings USING btree (ranking);
CREATE INDEX idx_user_benefit_rankings_updated_at ON user_benefit_rankings USING btree (updated_at);
CREATE INDEX idx_user_benefit_rankings_user_id ON user_benefit_rankings USING btree (user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions USING btree (expires_at);
CREATE INDEX idx_user_sessions_session_token ON user_sessions USING btree (session_token);
CREATE INDEX idx_user_sessions_user_id ON user_sessions USING btree (user_id);
CREATE INDEX idx_users_company_id ON users USING btree (company_id);
CREATE INDEX idx_users_email ON users USING btree (email);
CREATE INDEX idx_users_payment_status ON users USING btree (payment_status);

-- Triggers for updated_at columns
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_benefit_categories_updated_at BEFORE UPDATE ON benefit_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_missing_company_reports_updated_at BEFORE UPDATE ON missing_company_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON user_benefit_rankings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON benefit_removal_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_analytics_summary_updated_at BEFORE UPDATE ON daily_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_analytics_summary_updated_at BEFORE UPDATE ON company_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_locations_updated_at BEFORE UPDATE ON company_locations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cache_store_updated_at BEFORE UPDATE ON cache_store
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rate_limits_updated_at BEFORE UPDATE ON rate_limits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system benefit categories
INSERT INTO benefit_categories (name, display_name, description, icon, sort_order) VALUES
('health', 'Health & Medical', 'Health insurance, medical benefits, and healthcare-related perks', '🏥', 1),
('time_off', 'Time Off', 'Vacation days, sick leave, parental leave, and other time-off benefits', '🏖️', 2),
('financial', 'Financial', 'Retirement plans, stock options, bonuses, and financial benefits', '💰', 3),
('development', 'Development', 'Learning budgets, training programs, conference attendance, and professional development', '📚', 4),
('wellness', 'Wellness', 'Gym memberships, mental health support, wellness programs, and fitness benefits', '🧘', 5),
('work_life', 'Work-Life Balance', 'Flexible hours, remote work options, and work-life balance benefits', '⚖️', 6),
('other', 'Other Benefits', 'Miscellaneous benefits that do not fit into other categories', '🎁', 7);

-- Row Level Security (RLS) policies
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_removal_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_benefit_rankings ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_page_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_search_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_analytics_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_analytics_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_locations ENABLE ROW LEVEL SECURITY;

-- Public read access for companies and benefits
CREATE POLICY "Public companies are viewable by everyone" ON companies
    FOR SELECT USING (true);

CREATE POLICY "Public benefits are viewable by everyone" ON benefits
    FOR SELECT USING (true);

CREATE POLICY "Public company benefits are viewable by everyone" ON company_benefits
    FOR SELECT USING (true);

CREATE POLICY "Public company locations are viewable by everyone" ON company_locations
    FOR SELECT USING (true);

-- Benefit removal disputes are viewable by admins and the users who created them
CREATE POLICY "Benefit removal disputes are viewable by admins" ON benefit_removal_disputes
    FOR SELECT USING (true); -- Will be restricted by application logic

-- User benefit rankings are only viewable and editable by the user who created them
CREATE POLICY "Users can view their own benefit rankings" ON user_benefit_rankings
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can insert their own benefit rankings" ON user_benefit_rankings
    FOR INSERT WITH CHECK (true); -- Will be restricted by application logic

CREATE POLICY "Users can update their own benefit rankings" ON user_benefit_rankings
    FOR UPDATE USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can delete their own benefit rankings" ON user_benefit_rankings
    FOR DELETE USING (true); -- Will be restricted by application logic

-- Analytics RLS Policies (Admin can see all, users can see their own data)
CREATE POLICY "Admin can view all company page views" ON company_page_views
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all search queries" ON search_queries
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all benefit interactions" ON benefit_search_interactions
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all analytics summaries" ON daily_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all company analytics" ON company_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic

-- Company benefits junction table
CREATE TABLE company_benefits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    added_by VARCHAR(255), -- User ID from auth system
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, benefit_id)
);

-- Company users table (for company representatives)
CREATE TABLE company_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, email)
);

-- Benefit verifications table
CREATE TABLE benefit_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    status VARCHAR(50) CHECK (status IN ('confirmed', 'disputed')) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Saved companies table (for user bookmarks)
CREATE TABLE saved_companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, company_id)
);

-- Users table for local authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    payment_status VARCHAR(50) DEFAULT 'free' CHECK (payment_status IN ('free', 'paying')),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL
);

-- Sessions table for local authentication
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Magic link tokens table
CREATE TABLE magic_link_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    user_data JSONB,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Magic link rate limits table
CREATE TABLE magic_link_rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Company verification tokens table
CREATE TABLE company_verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_email VARCHAR(255) NOT NULL,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User benefit rankings table
CREATE TABLE user_benefit_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10), -- 1 = most important, 10 = least important
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, benefit_id)
);

-- Missing company reports table
CREATE TABLE missing_company_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_email VARCHAR(255) NOT NULL,
    email_domain VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    status VARCHAR(50) CHECK (status IN ('pending', 'reviewed', 'added', 'rejected')) DEFAULT 'pending',
    admin_notes TEXT,
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activity log table
CREATE TABLE activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    event_description TEXT NOT NULL,
    user_id VARCHAR(255),
    user_email VARCHAR(255),
    user_name VARCHAR(255),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    company_name VARCHAR(255),
    benefit_id UUID REFERENCES benefits(id) ON DELETE SET NULL,
    benefit_name VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Migration log table
CREATE TABLE migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefit removal disputes table
CREATE TABLE benefit_removal_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL, -- Reason for requesting removal
    status VARCHAR(50) CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')) DEFAULT 'pending',
    admin_user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Admin who approved/rejected
    admin_comment TEXT, -- Admin's comment on the decision
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure one dispute per user per benefit
    UNIQUE(company_benefit_id, user_id)
);

-- Company page views tracking
CREATE TABLE company_page_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- NULL for anonymous views
    session_id VARCHAR(255), -- For tracking anonymous sessions
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search queries tracking
CREATE TABLE search_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- NULL for anonymous searches
    session_id VARCHAR(255), -- For tracking anonymous sessions
    results_count INTEGER DEFAULT 0,
    filters_applied JSONB, -- Store applied filters as JSON
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefit search interactions (when users click on benefits in search results)
CREATE TABLE benefit_search_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_query_id UUID REFERENCES search_queries(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) CHECK (interaction_type IN ('view', 'click', 'verify', 'dispute')) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics summary tables for performance (daily aggregates)
CREATE TABLE daily_analytics_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    total_company_views INTEGER DEFAULT 0,
    total_searches INTEGER DEFAULT 0,
    total_benefit_interactions INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    unique_searchers INTEGER DEFAULT 0,
    top_searched_benefits JSONB, -- Array of {benefit_id, benefit_name, search_count}
    top_viewed_companies JSONB, -- Array of {company_id, company_name, view_count}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date)
);

-- Company analytics summary (for quick company-specific metrics)
CREATE TABLE company_analytics_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    page_views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    benefit_interactions INTEGER DEFAULT 0,
    search_appearances INTEGER DEFAULT 0, -- How many times company appeared in search results
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, date)
);



-- Indexes for better performance
CREATE INDEX idx_companies_location ON companies(location);
CREATE INDEX idx_companies_size ON companies(size);
CREATE INDEX idx_companies_industry ON companies(industry);
CREATE INDEX idx_companies_domain ON companies(domain);

-- Indexes for company locations
CREATE INDEX idx_company_locations_company_id ON company_locations(company_id);
CREATE INDEX idx_company_locations_normalized ON company_locations(location_normalized);
CREATE INDEX idx_company_locations_city ON company_locations(city);
CREATE INDEX idx_company_locations_country ON company_locations(country);
CREATE INDEX idx_company_locations_country_code ON company_locations(country_code);
CREATE INDEX idx_company_locations_primary ON company_locations(is_primary) WHERE is_primary = true;
CREATE INDEX idx_company_locations_headquarters ON company_locations(is_headquarters) WHERE is_headquarters = true;
CREATE INDEX idx_company_locations_type ON company_locations(location_type);
CREATE INDEX idx_company_locations_coords ON company_locations(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;



CREATE INDEX idx_benefits_category ON benefits(category);
CREATE INDEX idx_benefits_category_id ON benefits(category_id);
CREATE INDEX idx_benefits_name ON benefits(name);

CREATE INDEX idx_benefit_categories_name ON benefit_categories(name);
CREATE INDEX idx_benefit_categories_sort_order ON benefit_categories(sort_order);
CREATE INDEX idx_benefit_categories_is_active ON benefit_categories(is_active);

CREATE INDEX idx_company_benefits_company_id ON company_benefits(company_id);
CREATE INDEX idx_company_benefits_benefit_id ON company_benefits(benefit_id);
CREATE INDEX idx_company_benefits_verified ON company_benefits(is_verified);

CREATE INDEX idx_company_users_company_id ON company_users(company_id);
CREATE INDEX idx_company_users_email ON company_users(email);

CREATE INDEX idx_benefit_verifications_company_benefit_id ON benefit_verifications(company_benefit_id);
CREATE INDEX idx_benefit_verifications_user_id ON benefit_verifications(user_id);
CREATE INDEX idx_benefit_verifications_status ON benefit_verifications(status);

CREATE INDEX idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);
CREATE INDEX idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);
CREATE INDEX idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);
CREATE INDEX idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_payment_status ON users(payment_status);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);

CREATE INDEX idx_magic_link_tokens_email ON magic_link_tokens(email);
CREATE INDEX idx_magic_link_tokens_token ON magic_link_tokens(token);
CREATE INDEX idx_magic_link_rate_limits_email ON magic_link_rate_limits(email);
CREATE INDEX idx_magic_link_rate_limits_window_start ON magic_link_rate_limits(window_start);

CREATE INDEX idx_company_verification_tokens_company_id ON company_verification_tokens(company_id);
CREATE INDEX idx_company_verification_tokens_user_id ON company_verification_tokens(user_id);
CREATE INDEX idx_company_verification_tokens_token ON company_verification_tokens(token);

CREATE INDEX idx_missing_company_reports_email_domain ON missing_company_reports(email_domain);
CREATE INDEX idx_missing_company_reports_status ON missing_company_reports(status);

CREATE INDEX idx_activity_log_event_type ON activity_log(event_type);
CREATE INDEX idx_activity_log_user_id ON activity_log(user_id);
CREATE INDEX idx_activity_log_company_id ON activity_log(company_id);
CREATE INDEX idx_activity_log_created_at ON activity_log(created_at);

CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON benefit_removal_disputes(company_benefit_id);
CREATE INDEX idx_benefit_removal_disputes_user_id ON benefit_removal_disputes(user_id);
CREATE INDEX idx_benefit_removal_disputes_status ON benefit_removal_disputes(status);
CREATE INDEX idx_benefit_removal_disputes_created_at ON benefit_removal_disputes(created_at);

-- Analytics table indexes
CREATE INDEX idx_company_page_views_company_id ON company_page_views(company_id);
CREATE INDEX idx_company_page_views_user_id ON company_page_views(user_id);
CREATE INDEX idx_company_page_views_session_id ON company_page_views(session_id);
CREATE INDEX idx_company_page_views_created_at ON company_page_views(created_at);

CREATE INDEX idx_search_queries_user_id ON search_queries(user_id);
CREATE INDEX idx_search_queries_session_id ON search_queries(session_id);
CREATE INDEX idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX idx_search_queries_query_text ON search_queries USING gin(to_tsvector('english', query_text));

CREATE INDEX idx_benefit_search_interactions_search_query_id ON benefit_search_interactions(search_query_id);
CREATE INDEX idx_benefit_search_interactions_benefit_id ON benefit_search_interactions(benefit_id);
CREATE INDEX idx_benefit_search_interactions_company_id ON benefit_search_interactions(company_id);
CREATE INDEX idx_benefit_search_interactions_user_id ON benefit_search_interactions(user_id);
CREATE INDEX idx_benefit_search_interactions_created_at ON benefit_search_interactions(created_at);

CREATE INDEX idx_daily_analytics_summary_date ON daily_analytics_summary(date);
CREATE INDEX idx_company_analytics_summary_company_id ON company_analytics_summary(company_id);
CREATE INDEX idx_company_analytics_summary_date ON company_analytics_summary(date);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Clean up expired tokens function
CREATE OR REPLACE FUNCTION cleanup_expired_magic_links()
RETURNS void AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_benefit_categories_updated_at BEFORE UPDATE ON benefit_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_missing_company_reports_updated_at BEFORE UPDATE ON missing_company_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON user_benefit_rankings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON benefit_removal_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_analytics_summary_updated_at BEFORE UPDATE ON daily_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_analytics_summary_updated_at BEFORE UPDATE ON company_analytics_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system benefit categories
INSERT INTO benefit_categories (name, display_name, description, icon, sort_order, is_system) VALUES
('health', 'Health & Medical', 'Health insurance, medical benefits, and healthcare-related perks', '🏥', 1, true),
('time_off', 'Time Off', 'Vacation days, sick leave, parental leave, and other time-off benefits', '🏖️', 2, true),
('financial', 'Financial', 'Retirement plans, stock options, bonuses, and financial benefits', '💰', 3, true),
('development', 'Development', 'Learning budgets, training programs, conference attendance, and professional development', '📚', 4, true),
('wellness', 'Wellness', 'Gym memberships, mental health support, wellness programs, and fitness benefits', '🧘', 5, true),
('work_life', 'Work-Life Balance', 'Flexible hours, remote work options, and work-life balance benefits', '⚖️', 6, true),
('other', 'Other Benefits', 'Miscellaneous benefits that do not fit into other categories', '🎁', 7, true);

-- Row Level Security (RLS) policies
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_removal_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_benefit_rankings ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_page_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE benefit_search_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_analytics_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_analytics_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_locations ENABLE ROW LEVEL SECURITY;


-- Public read access for companies and benefits
CREATE POLICY "Public companies are viewable by everyone" ON companies
    FOR SELECT USING (true);

CREATE POLICY "Public benefits are viewable by everyone" ON benefits
    FOR SELECT USING (true);

CREATE POLICY "Public company benefits are viewable by everyone" ON company_benefits
    FOR SELECT USING (true);

CREATE POLICY "Public company locations are viewable by everyone" ON company_locations
    FOR SELECT USING (true);



-- Benefit removal disputes are viewable by admins and the users who created them
CREATE POLICY "Benefit removal disputes are viewable by admins" ON benefit_removal_disputes
    FOR SELECT USING (true); -- Will be restricted by application logic

-- User benefit rankings are only viewable and editable by the user who created them
CREATE POLICY "Users can view their own benefit rankings" ON user_benefit_rankings
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can insert their own benefit rankings" ON user_benefit_rankings
    FOR INSERT WITH CHECK (true); -- Will be restricted by application logic

CREATE POLICY "Users can update their own benefit rankings" ON user_benefit_rankings
    FOR UPDATE USING (true); -- Will be restricted by application logic

CREATE POLICY "Users can delete their own benefit rankings" ON user_benefit_rankings
    FOR DELETE USING (true); -- Will be restricted by application logic

-- Analytics RLS Policies (Admin can see all, users can see their own data)
CREATE POLICY "Admin can view all company page views" ON company_page_views
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all search queries" ON search_queries
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all benefit interactions" ON benefit_search_interactions
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all analytics summaries" ON daily_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic

CREATE POLICY "Admin can view all company analytics" ON company_analytics_summary
    FOR SELECT USING (true); -- Will be restricted by application logic
