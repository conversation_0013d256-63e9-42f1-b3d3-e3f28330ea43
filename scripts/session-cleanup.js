#!/usr/bin/env node

// Session cleanup script for PostgreSQL-based session management
// This script should be run periodically via cron to clean up expired sessions

import { config } from 'dotenv'
import { scheduledSessionCleanup } from '../src/lib/postgresql-session.ts'

// Load environment variables
config({ path: '.env.local' })

async function main() {
  console.log('Starting session cleanup...')
  
  try {
    await scheduledSessionCleanup()
    console.log('Session cleanup completed successfully')
    process.exit(0)
  } catch (error) {
    console.error('Session cleanup failed:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('Session cleanup interrupted')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('Session cleanup terminated')
  process.exit(1)
})

main()
