import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// PATCH /api/admin/users/[userId]/payment-status - Update user payment status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    await requireAdmin()
    
    const { userId } = await params
    const body = await request.json()
    const { payment_status } = body

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    if (!payment_status) {
      return NextResponse.json(
        { error: 'Payment status is required' },
        { status: 400 }
      )
    }

    // Validate payment status
    const validStatuses = ['free', 'paying']
    if (!validStatuses.includes(payment_status)) {
      return NextResponse.json(
        { error: 'Invalid payment status. Must be one of: free, paying' },
        { status: 400 }
      )
    }

    // Check if user exists
    const userResult = await query(
      'SELECT id, email, payment_status FROM users WHERE id = $1',
      [userId]
    )

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const currentUser = userResult.rows[0]

    // Update payment status
    const updateResult = await query(
      'UPDATE users SET payment_status = $1, updated_at = NOW() WHERE id = $2 RETURNING id, email, payment_status, updated_at',
      [payment_status, userId]
    )

    const updatedUser = updateResult.rows[0]

    const message = `Payment status updated from "${currentUser.payment_status}" to "${payment_status}"`

    return NextResponse.json({
      success: true,
      message,
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        payment_status: updatedUser.payment_status,
        updated_at: updatedUser.updated_at
      },
      previous_status: currentUser.payment_status
    })

  } catch (error) {
    console.error('Error updating payment status:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update payment status' },
      { status: 500 }
    )
  }
}

// GET /api/admin/users/[userId]/payment-status - Get user payment status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    await requireAdmin()
    
    const { userId } = await params

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Get user payment status
    const userResult = await query(
      'SELECT id, email, payment_status, updated_at FROM users WHERE id = $1',
      [userId]
    )

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const user = userResult.rows[0]

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        payment_status: user.payment_status,
        updated_at: user.updated_at
      }
    })

  } catch (error) {
    console.error('Error fetching payment status:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch payment status' },
      { status: 500 }
    )
  }
}
