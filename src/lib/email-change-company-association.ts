import { query } from '@/lib/local-db'
import { extractDomainFromEmail, isValidEmail } from '@/lib/utils'
import { getCompanyByDomain } from '@/lib/database'
import { sendEmail, EmailOptions } from '@/lib/email'
import { v4 as uuidv4 } from 'uuid'

// Common personal email domains that should not trigger company associations
const PERSONAL_EMAIL_DOMAINS = new Set([
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com',
  'aol.com', 'protonmail.com', 'tutanota.com', 'gmx.com', 'web.de',
  't-online.de', 'freenet.de', 'arcor.de', 'mail.de'
])

// Rate limiting for verification emails (max 3 per hour per user)
const VERIFICATION_EMAIL_RATE_LIMIT = 3
const VERIFICATION_EMAIL_WINDOW_HOURS = 1

/**
 * Checks if user has exceeded verification email rate limit
 */
export async function checkVerificationEmailRateLimit(userId: string): Promise<{
  allowed: boolean
  message: string
  remainingAttempts?: number
}> {
  try {
    const result = await query(`
      SELECT COUNT(*) as count
      FROM company_verification_tokens
      WHERE user_id = $1
        AND created_at > NOW() - INTERVAL '${VERIFICATION_EMAIL_WINDOW_HOURS} hours'
    `, [userId])

    const count = parseInt(result.rows[0]?.count || '0')
    const remainingAttempts = Math.max(0, VERIFICATION_EMAIL_RATE_LIMIT - count)

    if (count >= VERIFICATION_EMAIL_RATE_LIMIT) {
      return {
        allowed: false,
        message: `You can only request ${VERIFICATION_EMAIL_RATE_LIMIT} verification emails per ${VERIFICATION_EMAIL_WINDOW_HOURS} hour(s). Please try again later.`,
        remainingAttempts: 0
      }
    }

    return {
      allowed: true,
      message: `${remainingAttempts} verification email(s) remaining this hour.`,
      remainingAttempts
    }
  } catch (error) {
    console.error('Error checking verification email rate limit:', error)
    return {
      allowed: false,
      message: 'Unable to check rate limit. Please try again later.'
    }
  }
}

/**
 * Creates a verification token for company association
 */
export async function createCompanyVerificationToken(
  userId: string,
  userEmail: string,
  companyId: string
): Promise<string> {
  const token = uuidv4()
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

  try {
    await query(`
      INSERT INTO company_verification_tokens (
        token,
        user_id,
        user_email,
        company_id,
        expires_at,
        created_at
      ) VALUES ($1, $2, $3, $4, $5, NOW())
    `, [token, userId, userEmail, companyId, expiresAt])

    return token
  } catch (error) {
    console.error('Error creating company verification token:', error)
    throw new Error('Failed to create verification token')
  }
}

/**
 * Creates company verification email content
 */
export function createCompanyVerificationEmailContent(
  email: string,
  firstName: string,
  companyName: string,
  verificationUrl: string
): EmailOptions {
  const subject = `Verify your association with ${companyName}`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Company Association</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">BenefitLens</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Verify Your Company Association</p>
      </div>

      <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <h2 style="color: #333; margin-top: 0;">Hi ${firstName}!</h2>

        <p>We received a request to associate your account with <strong>${companyName}</strong>.</p>

        <p>To complete this association and access your company's benefits information, please click the button below:</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}"
             style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            Verify Company Association
          </a>
        </div>

        <p style="color: #666; font-size: 14px;">
          If the button doesn't work, you can copy and paste this link into your browser:<br>
          <a href="${verificationUrl}" style="color: #667eea; word-break: break-all;">${verificationUrl}</a>
        </p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0; font-size: 16px;">What happens next?</h3>
          <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
            <li>Your account will be associated with ${companyName}</li>
            <li>You'll be able to view and verify company benefits</li>
            <li>You can contribute to accurate benefit information</li>
          </ul>
        </div>

        <p style="color: #666; font-size: 14px; margin-top: 30px;">
          This verification link will expire in 24 hours. If you didn't request this association, you can safely ignore this email.
        </p>

        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">

        <p style="color: #999; font-size: 12px; text-align: center;">
          This email was sent to ${email}. If you have any questions, please contact our support team.
        </p>
      </div>
    </body>
    </html>
  `

  const text = `
Hi ${firstName}!

We received a request to associate your account with ${companyName}.

To complete this association and access your company's benefits information, please visit:
${verificationUrl}

What happens next?
- Your account will be associated with ${companyName}
- You'll be able to view and verify company benefits
- You can contribute to accurate benefit information

This verification link will expire in 24 hours. If you didn't request this association, you can safely ignore this email.

This email was sent to ${email}. If you have any questions, please contact our support team.
  `.trim()

  return {
    to: email,
    subject,
    html,
    text
  }
}

/**
 * Sends company verification email
 */
export async function sendCompanyVerificationEmail(
  email: string,
  firstName: string,
  companyName: string,
  token: string
): Promise<void> {
  const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/verify-company?token=${token}`

  const emailOptions = createCompanyVerificationEmailContent(
    email,
    firstName,
    companyName,
    verificationUrl
  )

  await sendEmail(emailOptions)
}

/**
 * Records that a verification email was sent (for rate limiting)
 */
export async function recordVerificationEmailSent(_userId: string): Promise<void> {
  // This is automatically handled by the createCompanyVerificationToken function
  // which inserts a record with the current timestamp
}

export interface EmailChangeCompanyResult {
  success: boolean
  action: 'no_change' | 'company_updated' | 'company_removed' | 'verification_required'
  previousCompanyId?: string | null
  newCompanyId?: string | null
  companyName?: string
  verificationToken?: string
  message: string
  error?: string
}

export interface CompanyVerificationToken {
  id: string
  token: string
  user_id: string
  user_email: string
  company_id: string
  expires_at: string
  used_at?: string
  created_at: string
}

/**
 * Handles company association changes when a user's email address is updated
 */
export async function handleEmailChangeCompanyAssociation(
  userId: string,
  oldEmail: string,
  newEmail: string,
  firstName?: string
): Promise<EmailChangeCompanyResult> {
  try {
    // Validate email format
    if (!isValidEmail(newEmail)) {
      return {
        success: false,
        action: 'no_change',
        message: 'Invalid email format',
        error: 'Invalid email format'
      }
    }

    const oldDomain = extractDomainFromEmail(oldEmail)
    const newDomain = extractDomainFromEmail(newEmail)

    // If domain hasn't changed, no action needed
    if (oldDomain === newDomain) {
      return {
        success: true,
        action: 'no_change',
        message: 'Email domain unchanged, no company association update needed'
      }
    }

    // Check if new domain is a personal email domain
    if (PERSONAL_EMAIL_DOMAINS.has(newDomain)) {
      // Remove company association if switching to personal email
      const currentUserResult = await query(
        'SELECT company_id FROM users WHERE id = $1',
        [userId]
      )

      if (currentUserResult.rows.length > 0 && currentUserResult.rows[0].company_id) {
        await query(
          'UPDATE users SET company_id = NULL WHERE id = $1',
          [userId]
        )

        return {
          success: true,
          action: 'company_removed',
          previousCompanyId: currentUserResult.rows[0].company_id,
          newCompanyId: null,
          message: 'Company association removed as new email is a personal email address'
        }
      }

      return {
        success: true,
        action: 'no_change',
        message: 'Personal email domain detected, no company association needed'
      }
    }

    // Get current user's company association
    const currentUserResult = await query(
      'SELECT company_id FROM users WHERE id = $1',
      [userId]
    )

    if (currentUserResult.rows.length === 0) {
      return {
        success: false,
        action: 'no_change',
        message: 'User not found',
        error: 'User not found'
      }
    }

    const currentCompanyId = currentUserResult.rows[0].company_id

    // Find company for new email domain
    const newCompany = await getCompanyByDomain(newDomain)

    // Case 1: New domain matches a company
    if (newCompany) {
      // If it's the same company, no change needed
      if (currentCompanyId === newCompany.id) {
        return {
          success: true,
          action: 'no_change',
          previousCompanyId: currentCompanyId,
          newCompanyId: newCompany.id,
          companyName: newCompany.name,
          message: `Email domain still matches ${newCompany.name}, no change needed`
        }
      }

      // Different company - require verification
      // Check rate limiting for verification emails
      const rateLimitCheck = await checkVerificationEmailRateLimit(userId)
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          action: 'verification_required',
          previousCompanyId: currentCompanyId,
          newCompanyId: newCompany.id,
          companyName: newCompany.name,
          message: `Rate limit exceeded. Please wait before requesting another verification email. ${rateLimitCheck.message}`,
          error: 'Rate limit exceeded'
        }
      }

      const verificationToken = await createCompanyVerificationToken(
        userId,
        newEmail,
        newCompany.id
      )

      // Send verification email
      await sendCompanyVerificationEmail(
        newEmail,
        firstName || 'User',
        newCompany.name,
        verificationToken
      )

      // Record verification email sent for rate limiting
      await recordVerificationEmailSent(userId)

      return {
        success: true,
        action: 'verification_required',
        previousCompanyId: currentCompanyId,
        newCompanyId: newCompany.id,
        companyName: newCompany.name,
        verificationToken,
        message: `Verification email sent for ${newCompany.name} association. Please check your email and click the verification link.`
      }
    }

    // Case 2: New domain doesn't match any company - remove association
    if (currentCompanyId) {
      await query(
        'UPDATE users SET company_id = NULL WHERE id = $1',
        [userId]
      )

      return {
        success: true,
        action: 'company_removed',
        previousCompanyId: currentCompanyId,
        newCompanyId: null,
        message: 'Company association removed as new email domain does not match any registered company'
      }
    }

    // Case 3: No current company and new domain doesn't match - no change
    return {
      success: true,
      action: 'no_change',
      message: 'No company association changes needed'
    }

  } catch (error) {
    console.error('Error handling email change company association:', error)
    return {
      success: false,
      action: 'no_change',
      message: 'Failed to process company association change',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}



/**
 * Verifies a company association token and updates user's company
 */
export async function verifyCompanyAssociationToken(token: string): Promise<{
  success: boolean
  userId?: string
  companyId?: string
  companyName?: string
  message: string
  error?: string
}> {
  try {
    // Find and validate token
    const tokenResult = await query(
      `SELECT cvt.*, c.name as company_name
       FROM company_verification_tokens cvt
       JOIN companies c ON cvt.company_id = c.id
       WHERE cvt.token = $1 AND cvt.used_at IS NULL AND cvt.expires_at > NOW()`,
      [token]
    )

    if (tokenResult.rows.length === 0) {
      return {
        success: false,
        message: 'Invalid or expired verification token',
        error: 'Token not found or expired'
      }
    }

    const tokenData = tokenResult.rows[0]

    // Update user's company association
    await query(
      'UPDATE users SET company_id = $1 WHERE id = $2',
      [tokenData.company_id, tokenData.user_id]
    )

    // Mark token as used
    await query(
      'UPDATE company_verification_tokens SET used_at = NOW() WHERE token = $1',
      [token]
    )

    return {
      success: true,
      userId: tokenData.user_id,
      companyId: tokenData.company_id,
      companyName: tokenData.company_name,
      message: `Successfully associated with ${tokenData.company_name}`
    }

  } catch (error) {
    console.error('Error verifying company association token:', error)
    return {
      success: false,
      message: 'Failed to verify company association',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
