import { NextRequest, NextResponse } from 'next/server'
import { getCompanyBenefits, getCompanyById } from '@/lib/database'
import { canUserManageCompany } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'json'
    
    // Check if user can manage this company (optional for export)
    const canManage = await canUserManageCompany(companyId)
    
    const [company, benefits] = await Promise.all([
      getCompanyById(companyId),
      getCompanyBenefits(companyId)
    ])

    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const exportData = {
      company: {
        id: company.id,
        name: company.name,
        location: company.location,
        industry: company.industry,
        verified: company.verified
      },
      benefits: benefits.map(benefit => ({
        id: benefit.benefit_id,
        name: benefit.name,
        category: benefit.category,
        icon: benefit.icon,
        is_verified: benefit.is_verified,
        added_by: benefit.added_by,
        created_at: benefit.created_at
      })),
      exported_at: new Date().toISOString(),
      exported_by_authorized_user: canManage
    }

    if (format === 'csv') {
      // Generate CSV format
      const csvHeaders = 'Benefit Name,Category,Icon,Verified,Added By,Created At\n'
      const csvRows = benefits.map(benefit => 
        `"${benefit.name}","${benefit.category}","${benefit.icon || ''}","${benefit.is_verified}","${benefit.added_by || ''}","${benefit.created_at}"`
      ).join('\n')
      
      const csvContent = csvHeaders + csvRows

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${company.name.replace(/[^a-zA-Z0-9]/g, '_')}_benefits.csv"`
        }
      })
    }

    // Default JSON format
    return NextResponse.json(exportData, {
      headers: {
        'Content-Disposition': `attachment; filename="${company.name.replace(/[^a-zA-Z0-9]/g, '_')}_benefits.json"`
      }
    })

  } catch (error) {
    console.error('Error exporting company benefits:', error)
    return NextResponse.json(
      { error: 'Failed to export company benefits' },
      { status: 500 }
    )
  }
}
