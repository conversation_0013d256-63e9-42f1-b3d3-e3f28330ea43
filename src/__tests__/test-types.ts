import { vi } from 'vitest'

// Mock fetch response type
export interface MockResponse {
  ok: boolean
  status?: number
  statusText?: string
  json: () => Promise<unknown>
  text?: () => Promise<string>
  headers?: Headers
}

// Mock fetch function type
export type MockFetch = ReturnType<typeof vi.fn>

// Database query result types
export interface QueryResult<T = unknown> {
  rows: T[]
  rowCount: number
  command: string
}

// Mock database client
export interface MockDatabaseClient {
  query: ReturnType<typeof vi.fn>
  end: ReturnType<typeof vi.fn>
}

// Common API response types
export interface ApiSuccessResponse<T = unknown> {
  success: true
  data?: T
  message?: string
}

export interface ApiErrorResponse {
  success: false
  error: string
  details?: string
}

export type ApiResponse<T = unknown> = ApiSuccessResponse<T> | ApiErrorResponse

// Analytics data types
export interface AnalyticsData {
  totalUsers: number
  totalCompanies: number
  totalBenefits: number
  totalSearches: number
  totalSavedCompanies: number
  totalBenefitRankings: number
  recentActivity: ActivityRecord[]
}

export interface ActivityRecord {
  id: string
  user_id: string
  action: string
  details: Record<string, unknown>
  created_at: string
}

// Company and benefit types for tests
export interface TestCompany {
  id: string
  name: string
  industry?: string
  location?: string
  website?: string
  description?: string
  created_at?: string
  updated_at?: string
}

export interface TestBenefit {
  id: string
  name: string
  category?: string
  description?: string
  created_at?: string
  updated_at?: string
}

export interface TestCompanyBenefit {
  id: string
  company_id: string
  benefit_id: string
  verified: boolean
  created_at?: string
  updated_at?: string
}

// User types for tests
export interface TestUser {
  id: string
  email: string
  name?: string
  company_domain?: string
  is_admin?: boolean
  is_paying_customer?: boolean
  created_at?: string
  updated_at?: string
}

// Session types for tests
export interface TestSession {
  id: string
  user_id: string
  expires_at: string
  created_at?: string
}

// Helper function to create mock fetch responses
export function createMockResponse(data: unknown, options: Partial<MockResponse> = {}): MockResponse {
  return {
    ok: true,
    status: 200,
    statusText: 'OK',
    json: async () => data,
    text: async () => JSON.stringify(data),
    headers: new Headers(),
    ...options
  }
}

// Helper function to create mock error responses
export function createMockErrorResponse(error: string, status = 400): MockResponse {
  return createMockResponse(
    { success: false, error },
    { ok: false, status, statusText: 'Bad Request' }
  )
}

// Helper function to create mock database query results
export function createMockQueryResult<T>(rows: T[], command = 'SELECT'): QueryResult<T> {
  return {
    rows,
    rowCount: rows.length,
    command
  }
}
