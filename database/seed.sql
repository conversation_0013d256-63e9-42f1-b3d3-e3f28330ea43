-- BenefitLens Database Seed Data
-- This file contains sample data for development and testing
-- Updated to match current schema structure

-- Note: benefit_categories are inserted automatically via init.sql

-- Insert sample benefits with proper category_id references (removed category column)
INSERT INTO benefits (name, icon, description, category_id) VALUES
-- Health benefits
('Gym Membership', '💪', 'Access to company gym or gym membership reimbursement', (SELECT id FROM benefit_categories WHERE name = 'wellness')),
('Dental Insurance Plus (Zahnzusatzversicherung)', '🦷', 'Additional dental insurance coverage beyond statutory requirements', (SELECT id FROM benefit_categories WHERE name = 'health')),
('Company Health Check-ups (Betriebsärztliche Untersuchungen)', '🩺', 'Regular health screenings and medical check-ups provided by company', (SELECT id FROM benefit_categories WHERE name = 'health')),
('Mental Health Support (Psychologische Betreuung)', '🧠', 'Access to mental health counseling and psychological support services', (SELECT id FROM benefit_categories WHERE name = 'health')),
('Occupational Health Services (Arbeitsmedizin)', '⚕️', 'Workplace health and safety medical services', (SELECT id FROM benefit_categories WHERE name = 'health')),
('Private Health Insurance (Private Krankenversicherung)', '🏥', 'Premium private health insurance coverage', (SELECT id FROM benefit_categories WHERE name = 'health')),

-- Time off benefits
('Unlimited PTO', '🏖️', 'Unlimited paid time off policy', (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Extra Vacation Days', '🌴', 'Additional vacation days beyond legal minimum', (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Sabbatical Leave', '📚', 'Extended leave for personal development or rest', (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Parental Leave Plus', '👶', 'Enhanced parental leave beyond legal requirements', (SELECT id FROM benefit_categories WHERE name = 'time_off')),

-- Financial benefits
('Stock Options', '📈', 'Employee stock option program', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Retirement Plan', '💰', 'Company retirement savings plan', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Holiday Bonus (Urlaubsgeld)', '🏖️', 'Additional payment for vacation expenses', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Company Pension Scheme (Betriebliche Altersvorsorge)', '💰', 'Employer-sponsored pension plan', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Profit Sharing (Gewinnbeteiligung)', '📈', 'Share in company profits distributed to employees', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Employee Stock Purchase Plan (Mitarbeiterbeteiligung)', '📊', 'Discounted company stock purchase program', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Performance Bonus', '🎯', 'Annual performance-based bonus payments', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Bike Leasing (Dienstradleasing)', '🚲', 'Company bike leasing program for commuting', (SELECT id FROM benefit_categories WHERE name = 'financial')),

-- Development benefits
('Learning Budget', '📚', 'Annual budget for courses, conferences, and training', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Conference Attendance', '🎤', 'Paid attendance at industry conferences and events', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Certification Support', '🏆', 'Financial support for professional certifications', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Mentorship Program', '👥', 'Structured mentorship and career development program', (SELECT id FROM benefit_categories WHERE name = 'development')),

-- Wellness benefits
('Massage Services', '💆', 'On-site massage therapy services', (SELECT id FROM benefit_categories WHERE name = 'wellness')),
('Yoga Classes', '🧘‍♀️', 'Company-sponsored yoga and fitness classes', (SELECT id FROM benefit_categories WHERE name = 'wellness')),
('Wellness Stipend', '🌱', 'Monthly allowance for wellness activities', (SELECT id FROM benefit_categories WHERE name = 'wellness')),

-- Work-life balance benefits
('Remote Work', '🏠', 'Flexible remote work options', (SELECT id FROM benefit_categories WHERE name = 'work_life')),
('Flexible Hours', '⏰', 'Flexible working hours and core time', (SELECT id FROM benefit_categories WHERE name = 'work_life')),
('4-Day Work Week', '📅', 'Compressed work week with 4 working days', (SELECT id FROM benefit_categories WHERE name = 'work_life')),
('Childcare Support', '👶', 'On-site childcare or childcare assistance', (SELECT id FROM benefit_categories WHERE name = 'work_life')),

-- Other benefits
('Free Lunch', '🍽️', 'Complimentary meals provided by company', (SELECT id FROM benefit_categories WHERE name = 'other')),
('Pet-Friendly Office', '🐕', 'Pets allowed in the workplace', (SELECT id FROM benefit_categories WHERE name = 'other')),
('Company Car', '🚗', 'Company vehicle for business and personal use', (SELECT id FROM benefit_categories WHERE name = 'other')),
('Employee Discounts', '🛍️', 'Discounts on company products and partner services', (SELECT id FROM benefit_categories WHERE name = 'other')),
('Team Events', '🎉', 'Regular team building events and company parties', (SELECT id FROM benefit_categories WHERE name = 'other'));

-- Insert sample companies (removed location column, added new columns)
INSERT INTO companies (name, size, industry, description, domain, career_url, website, founded_year) VALUES
('SAP', 'enterprise', 'Technology', 'Global leader in enterprise software solutions', 'sap.com', 'https://jobs.sap.com', 'https://www.sap.com', 1972),
('Deutsche Bank', 'enterprise', 'Financial Services', 'Leading global investment bank and financial services company', 'db.com', 'https://careers.db.com', 'https://www.db.com', 1870),
('Accenture', 'enterprise', 'Consulting', 'Global professional services company with leading capabilities in digital, cloud and security', 'accenture.com', 'https://www.accenture.com/careers', 'https://www.accenture.com', 1989),
('PwC', 'large', 'Consulting', 'One of the Big Four accounting firms providing audit, tax and consulting services', 'pwc.com', 'https://www.pwc.com/careers', 'https://www.pwc.com', 1998),
('Commerzbank', 'large', 'Financial Services', 'Major German commercial bank', 'commerzbank.de', 'https://karriere.commerzbank.de', 'https://www.commerzbank.de', 1870),
('DZ Bank', 'large', 'Financial Services', 'Central institution for cooperative banks in Germany', 'dzbank.de', 'https://karriere.dzbank.de', 'https://www.dzbank.de', 1949),
('Lufthansa', 'enterprise', 'Aviation', 'German airline and aviation group', 'lufthansa.com', 'https://careers.lufthansagroup.com', 'https://www.lufthansa.com', 1953),
('Siemens', 'enterprise', 'Technology', 'Global technology company focused on industry, infrastructure, transport, and healthcare', 'siemens.com', 'https://jobs.siemens.com', 'https://www.siemens.com', 1847),
('Spotify', 'large', 'Technology', 'Audio streaming and media services provider', 'spotify.com', 'https://www.lifeatspotify.com', 'https://www.spotify.com', 2006),
('N26', 'medium', 'Financial Services', 'Digital bank offering mobile banking solutions', 'n26.com', 'https://n26.com/en/careers', 'https://n26.com', 2013),
('Zalando', 'large', 'E-commerce', 'European online fashion and lifestyle platform', 'zalando.com', 'https://jobs.zalando.com', 'https://www.zalando.com', 2008),
('Delivery Hero', 'large', 'Technology', 'Global online food delivery marketplace', 'deliveryhero.com', 'https://careers.deliveryhero.com', 'https://www.deliveryhero.com', 2011),
('HelloFresh', 'large', 'E-commerce', 'Meal kit delivery service', 'hellofresh.com', 'https://careers.hellofresh.com', 'https://www.hellofresh.com', 2011),
('Rocket Internet', 'medium', 'Technology', 'Internet company incubator and venture capital firm', 'rocket-internet.com', 'https://www.rocket-internet.com/careers', 'https://www.rocket-internet.com', 2007),
('FlixBus', 'medium', 'Transportation', 'Long-distance bus service provider', 'flixbus.com', 'https://careers.flixbus.com', 'https://www.flixbus.com', 2013);

-- Insert company locations (new table for multi-location support)
INSERT INTO company_locations (company_id, location_raw, location_normalized, city, country, country_code, is_primary, is_headquarters, location_type) VALUES
-- SAP locations
((SELECT id FROM companies WHERE name = 'SAP'), 'Walldorf, Germany', 'Walldorf, Germany', 'Walldorf', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'SAP'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', false, false, 'office'),
((SELECT id FROM companies WHERE name = 'SAP'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', false, false, 'office'),

-- Deutsche Bank locations
((SELECT id FROM companies WHERE name = 'Deutsche Bank'), 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Deutsche Bank'), 'London, UK', 'London, United Kingdom', 'London', 'United Kingdom', 'GB', false, false, 'office'),
((SELECT id FROM companies WHERE name = 'Deutsche Bank'), 'New York, USA', 'New York, United States', 'New York', 'United States', 'US', false, false, 'office'),

-- Accenture locations
((SELECT id FROM companies WHERE name = 'Accenture'), 'Dublin, Ireland', 'Dublin, Ireland', 'Dublin', 'Ireland', 'IE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Accenture'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', false, false, 'office'),
((SELECT id FROM companies WHERE name = 'Accenture'), 'Vienna, Austria', 'Vienna, Austria', 'Vienna', 'Austria', 'AT', false, false, 'office'),

-- PwC locations
((SELECT id FROM companies WHERE name = 'PwC'), 'London, UK', 'London, United Kingdom', 'London', 'United Kingdom', 'GB', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'PwC'), 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', false, false, 'office'),
((SELECT id FROM companies WHERE name = 'PwC'), 'Düsseldorf, Germany', 'Düsseldorf, Germany', 'Düsseldorf', 'Germany', 'DE', false, false, 'office'),

-- Commerzbank locations
((SELECT id FROM companies WHERE name = 'Commerzbank'), 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Commerzbank'), 'Hamburg, Germany', 'Hamburg, Germany', 'Hamburg', 'Germany', 'DE', false, false, 'office'),

-- DZ Bank locations
((SELECT id FROM companies WHERE name = 'DZ Bank'), 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', true, true, 'headquarters'),

-- Lufthansa locations
((SELECT id FROM companies WHERE name = 'Lufthansa'), 'Cologne, Germany', 'Cologne, Germany', 'Cologne', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Lufthansa'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', false, false, 'office'),
((SELECT id FROM companies WHERE name = 'Lufthansa'), 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', false, false, 'office'),

-- Siemens locations
((SELECT id FROM companies WHERE name = 'Siemens'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Siemens'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', false, false, 'office'),
((SELECT id FROM companies WHERE name = 'Siemens'), 'Erlangen, Germany', 'Erlangen, Germany', 'Erlangen', 'Germany', 'DE', false, false, 'office'),

-- Spotify locations
((SELECT id FROM companies WHERE name = 'Spotify'), 'Stockholm, Sweden', 'Stockholm, Sweden', 'Stockholm', 'Sweden', 'SE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Spotify'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', false, false, 'office'),

-- N26 locations
((SELECT id FROM companies WHERE name = 'N26'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters'),

-- Zalando locations
((SELECT id FROM companies WHERE name = 'Zalando'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Zalando'), 'Dortmund, Germany', 'Dortmund, Germany', 'Dortmund', 'Germany', 'DE', false, false, 'office'),

-- Delivery Hero locations
((SELECT id FROM companies WHERE name = 'Delivery Hero'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters'),

-- HelloFresh locations
((SELECT id FROM companies WHERE name = 'HelloFresh'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters'),

-- Rocket Internet locations
((SELECT id FROM companies WHERE name = 'Rocket Internet'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters'),

-- FlixBus locations
((SELECT id FROM companies WHERE name = 'FlixBus'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'FlixBus'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', false, false, 'office');

-- Insert sample company benefits (linking companies with benefits)
-- This creates realistic benefit associations for the sample companies
INSERT INTO company_benefits (company_id, benefit_id, is_verified)
SELECT
    c.id,
    b.id,
    true
FROM companies c
CROSS JOIN benefits b
WHERE
    -- Large tech companies (comprehensive benefits)
    (c.name = 'SAP' AND b.name IN ('Gym Membership', 'Learning Budget', 'Stock Options', 'Mental Health Support (Psychologische Betreuung)', 'Remote Work', 'Flexible Hours', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Bike Leasing (Dienstradleasing)')) OR
    (c.name = 'Siemens' AND b.name IN ('Learning Budget', 'Stock Options', 'Gym Membership', 'Mental Health Support (Psychologische Betreuung)', 'Occupational Health Services (Arbeitsmedizin)', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Flexible Hours', 'Conference Attendance')) OR

    -- Financial services (traditional benefits)
    (c.name = 'Deutsche Bank' AND b.name IN ('Retirement Plan', 'Stock Options', 'Gym Membership', 'Free Lunch', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Private Health Insurance (Private Krankenversicherung)', 'Performance Bonus', 'Dental Insurance Plus (Zahnzusatzversicherung)')) OR
    (c.name = 'Commerzbank' AND b.name IN ('Retirement Plan', 'Stock Options', 'Gym Membership', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Holiday Bonus (Urlaubsgeld)', 'Company Health Check-ups (Betriebsärztliche Untersuchungen)')) OR
    (c.name = 'DZ Bank' AND b.name IN ('Retirement Plan', 'Learning Budget', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Flexible Hours', 'Employee Discounts')) OR

    -- Consulting firms (learning focused)
    (c.name = 'Accenture' AND b.name IN ('Learning Budget', 'Mental Health Support (Psychologische Betreuung)', 'Gym Membership', 'Profit Sharing (Gewinnbeteiligung)', 'Conference Attendance', 'Certification Support', 'Remote Work', 'Flexible Hours')) OR
    (c.name = 'PwC' AND b.name IN ('Learning Budget', 'Gym Membership', 'Company Health Check-ups (Betriebsärztliche Untersuchungen)', 'Holiday Bonus (Urlaubsgeld)', 'Conference Attendance', 'Mentorship Program', 'Flexible Hours')) OR

    -- Aviation (traditional + travel benefits)
    (c.name = 'Lufthansa' AND b.name IN ('Retirement Plan', 'Free Lunch', 'Unlimited PTO', 'Employee Stock Purchase Plan (Mitarbeiterbeteiligung)', 'Employee Discounts', 'Company Health Check-ups (Betriebsärztliche Untersuchungen)', 'Team Events')) OR

    -- Modern tech companies (progressive benefits)
    (c.name = 'Spotify' AND b.name IN ('Remote Work', 'Flexible Hours', 'Unlimited PTO', 'Learning Budget', 'Mental Health Support (Psychologische Betreuung)', 'Gym Membership', 'Free Lunch', 'Stock Options', 'Wellness Stipend', 'Team Events')) OR
    (c.name = 'N26' AND b.name IN ('Remote Work', 'Flexible Hours', 'Learning Budget', 'Stock Options', 'Mental Health Support (Psychologische Betreuung)', 'Gym Membership', 'Free Lunch', 'Bike Leasing (Dienstradleasing)', 'Team Events')) OR
    (c.name = 'Zalando' AND b.name IN ('Remote Work', 'Flexible Hours', 'Learning Budget', 'Stock Options', 'Employee Discounts', 'Gym Membership', 'Free Lunch', 'Mental Health Support (Psychologische Betreuung)', 'Wellness Stipend', 'Pet-Friendly Office')) OR
    (c.name = 'Delivery Hero' AND b.name IN ('Remote Work', 'Flexible Hours', 'Stock Options', 'Learning Budget', 'Free Lunch', 'Employee Discounts', 'Mental Health Support (Psychologische Betreuung)', 'Team Events')) OR
    (c.name = 'HelloFresh' AND b.name IN ('Remote Work', 'Flexible Hours', 'Stock Options', 'Free Lunch', 'Employee Discounts', 'Learning Budget', 'Gym Membership', 'Team Events')) OR
    (c.name = 'Rocket Internet' AND b.name IN ('Remote Work', 'Flexible Hours', 'Stock Options', 'Learning Budget', 'Free Lunch', 'Mental Health Support (Psychologische Betreuung)')) OR
    (c.name = 'FlixBus' AND b.name IN ('Remote Work', 'Flexible Hours', 'Learning Budget', 'Employee Discounts', 'Free Lunch', 'Bike Leasing (Dienstradleasing)', 'Team Events'));

-- Insert sample admin user (for testing purposes)
INSERT INTO users (email, first_name, last_name, email_verified, role, payment_status) VALUES
('<EMAIL>', 'Admin', 'User', true, 'admin', 'free'),
('<EMAIL>', 'Sarah', 'Admin', true, 'admin', 'paying');

-- Insert sample regular users with company associations
INSERT INTO users (email, first_name, last_name, email_verified, role, payment_status, company_id) VALUES
-- SAP employees
('<EMAIL>', 'John', 'Doe', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'SAP')),
('<EMAIL>', 'Maria', 'Garcia', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'SAP')),

-- Deutsche Bank employees
('<EMAIL>', 'Jane', 'Smith', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'Deutsche Bank')),
('<EMAIL>', 'Thomas', 'Mueller', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Deutsche Bank')),

-- Accenture consultants
('<EMAIL>', 'Mike', 'Johnson', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Accenture')),
('<EMAIL>', 'Anna', 'Kowalski', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'Accenture')),

-- Tech company employees
('<EMAIL>', 'Alex', 'Chen', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Spotify')),
('<EMAIL>', 'Lisa', 'Weber', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'N26')),
('<EMAIL>', 'David', 'Brown', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'Zalando')),
('<EMAIL>', 'Emma', 'Jones', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Delivery Hero')),

-- Users without company association (job seekers)
('<EMAIL>', 'Robert', 'Wilson', true, 'user', 'free', NULL),
('<EMAIL>', 'Sophie', 'Taylor', true, 'user', 'paying', NULL),
('<EMAIL>', 'Max', 'Freelancer', true, 'user', 'free', NULL);

-- Insert sample user benefit rankings (for testing analytics)
INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking) VALUES
-- John Doe's rankings (SAP employee)
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Remote Work'), 1),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Learning Budget'), 2),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Stock Options'), 3),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Flexible Hours'), 4),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Mental Health Support (Psychologische Betreuung)'), 5),

-- Jane Smith's rankings (Deutsche Bank employee)
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Company Pension Scheme (Betriebliche Altersvorsorge)'), 1),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Private Health Insurance (Private Krankenversicherung)'), 2),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Performance Bonus'), 3),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Flexible Hours'), 4),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Free Lunch'), 5),

-- Alex Chen's rankings (Spotify employee)
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Unlimited PTO'), 1),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Remote Work'), 2),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Stock Options'), 3),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Learning Budget'), 4),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Wellness Stipend'), 5);

-- Insert some saved companies (user bookmarks)
INSERT INTO saved_companies (user_id, company_id) VALUES
-- Job seekers saving companies they're interested in
('<EMAIL>', (SELECT id FROM companies WHERE name = 'SAP')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'Spotify')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'N26')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'Zalando')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'HelloFresh')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'Rocket Internet'));

-- Insert some sample activity log entries
INSERT INTO activity_log (event_type, event_description, user_email, user_name, company_name, benefit_name, created_at) VALUES
('user_registered', 'New user registered', '<EMAIL>', 'John Doe', NULL, NULL, NOW() - INTERVAL '7 days'),
('user_registered', 'New user registered', '<EMAIL>', 'Jane Smith', NULL, NULL, NOW() - INTERVAL '5 days'),
('benefit_verified', 'User verified company benefit', '<EMAIL>', 'John Doe', 'SAP', 'Remote Work', NOW() - INTERVAL '3 days'),
('benefit_verified', 'User verified company benefit', '<EMAIL>', 'Jane Smith', 'Deutsche Bank', 'Free Lunch', NOW() - INTERVAL '2 days'),
('user_registered', 'New user registered', '<EMAIL>', 'Alex Chen', NULL, NULL, NOW() - INTERVAL '1 day');
