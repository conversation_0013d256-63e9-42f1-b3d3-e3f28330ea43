import { getCurrentUser } from './auth'
import type { LocalUser } from './local-auth'

export type AnalyticsAccessLevel = 'full' | 'demo' | 'none'

export interface AnalyticsAccessInfo {
  level: AnalyticsAccessLevel
  user: LocalUser | null
  canAccessRealData: boolean
  isDemoMode: boolean
  upgradeRequired: boolean
}

/**
 * Check if a user has paying status
 */
export function isPayingUser(user: LocalUser | null): boolean {
  if (!user) {return false}
  return user.paymentStatus === 'paying'
}



/**
 * Check if a user is on free plan
 */
export function isFreeUser(user: LocalUser | null): boolean {
  if (!user) {return false}
  return user.paymentStatus === 'free'
}

/**
 * Determine analytics access level for a user
 */
export function getAnalyticsAccessLevel(user: LocalUser | null): AnalyticsAccessLevel {
  if (!user) {return 'none'}

  if (isPayingUser(user)) {
    return 'full'
  }

  // Free users get demo access
  if (isF<PERSON><PERSON><PERSON>(user)) {
    return 'demo'
  }

  return 'none'
}

/**
 * Get comprehensive analytics access information for current user
 */
export async function getAnalyticsAccessInfo(): Promise<AnalyticsAccessInfo> {
  const user = await getCurrentUser()
  const level = getAnalyticsAccessLevel(user)

  return {
    level,
    user,
    canAccessRealData: level === 'full',
    isDemoMode: level === 'demo',
    upgradeRequired: level !== 'full' && !!user
  }
}

/**
 * Require analytics access (throws if user doesn't have at least demo access)
 */
export async function requireAnalyticsAccess(): Promise<AnalyticsAccessInfo> {
  const accessInfo = await getAnalyticsAccessInfo()
  
  if (accessInfo.level === 'none') {
    throw new Error('Authentication required to access analytics')
  }
  
  return accessInfo
}

/**
 * Require full analytics access (throws if user doesn't have paying status)
 */
export async function requireFullAnalyticsAccess(): Promise<AnalyticsAccessInfo> {
  const accessInfo = await getAnalyticsAccessInfo()
  
  if (accessInfo.level !== 'full') {
    throw new Error('Premium subscription required for full analytics access')
  }
  
  return accessInfo
}

/**
 * Check if user can access company-specific analytics
 */
export async function canAccessCompanyAnalytics(companyId: string): Promise<boolean> {
  const accessInfo = await getAnalyticsAccessInfo()
  
  // Must have at least demo access
  if (accessInfo.level === 'none') {
    return false
  }
  
  // For demo users, we can show demo company analytics
  // For paying users, we need to check if they can manage the company
  if (accessInfo.level === 'demo') {
    return true // Demo data for any company
  }
  
  // For full access, check company management permissions
  const { canUserManageCompany } = await import('./auth')
  return await canUserManageCompany(companyId)
}

/**
 * Get payment status display text
 */
export function getPaymentStatusDisplay(paymentStatus: string): string {
  switch (paymentStatus) {
    case 'paying':
      return 'Premium'
    case 'free':
      return 'Free'
    default:
      return 'Unknown'
  }
}

/**
 * Get analytics feature availability based on payment status
 */
export function getAnalyticsFeatures(user: LocalUser | null) {
  const level = getAnalyticsAccessLevel(user)

  return {
    realTimeData: level === 'full',
    historicalData: level === 'full',
    exportData: level === 'full',
    advancedFilters: level === 'full',
    companyAnalytics: level !== 'none',
    searchTrends: level !== 'none',
    topCompanies: level !== 'none',
    demoDataOnly: level === 'demo',
    upgradePrompts: level !== 'full'
  }
}
