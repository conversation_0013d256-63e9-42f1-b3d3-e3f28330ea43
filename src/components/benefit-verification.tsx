'use client'

import { useState, useEffect, useCallback } from 'react'
import { CheckCircle, MessageSquare } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface BenefitVerificationProps {
  companyBenefitId: string
  benefitName: string
  companyName: string
  benefitId?: string
  companyId?: string
  onVerificationComplete?: () => void
  companyAuthStatus?: AuthorizationStatus | null
  hideAuthRestriction?: boolean
}

interface AuthorizationStatus {
  authorized: boolean
  message: string
  requiresAuth?: boolean
  companyName?: string
  requiredDomain?: string
  userDomain?: string
}

export function BenefitVerification({
  companyBenefitId,
  benefitName,
  companyName,
  benefitId,
  companyId,
  onVerificationComplete: _onVerificationComplete,
  companyAuthStatus,
  hideAuthRestriction: _hideAuthRestriction = false
}: BenefitVerificationProps) {
  const [isSignedIn, setIsSignedIn] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [authStatus, setAuthStatus] = useState<AuthorizationStatus | null>(null)
  const [isLoadingAuth, setIsLoadingAuth] = useState(true)

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/me')
      setIsSignedIn(response.ok)
    } catch {
      setIsSignedIn(false)
    }
  }

  const checkAuthorization = useCallback(async () => {
    try {
      setIsLoadingAuth(true)
      const response = await fetch(`/api/benefit-verifications/${companyBenefitId}/authorization`)
      if (response.ok) {
        const authResult = await response.json()
        setAuthStatus(authResult.status)
      } else {
        setAuthStatus({ authorized: false, message: 'Unauthorized' })
      }
    } catch (error) {
      console.error('Error checking authorization:', error)
      setAuthStatus({ authorized: false, message: 'Authorization check failed' })
    } finally {
      setIsLoadingAuth(false)
    }
  }, [companyBenefitId])

  const submitVerification = async (status: 'confirmed', comment: string) => {
    setIsSubmitting(true)
    try {
      const response = await fetch('/api/benefit-verifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyBenefitId: companyBenefitId,
          status,
          comment
        })
      })

      if (response.ok) {
        _onVerificationComplete?.()

        // Track analytics
        if (benefitId && companyId) {
          try {
            await fetch('/api/analytics/track', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                type: 'benefit_interaction',
                data: {
                  benefitId: benefitId,
                  companyId: companyId,
                  action: 'verified'
                }
              })
            })
          } catch (analyticsError) {
            console.error('Analytics tracking failed:', analyticsError)
          }
        }
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to submit verification')
      }
    } catch (error) {
      console.error('Error submitting verification:', error)
      alert('Failed to submit verification')
    } finally {
      setIsSubmitting(false)
    }
  }

  useEffect(() => {
    checkAuthStatus()

    // Use provided company auth status if available, otherwise check authorization
    if (companyAuthStatus !== undefined) {
      setAuthStatus(companyAuthStatus)
      setIsLoadingAuth(false)
    } else {
      checkAuthorization()
    }
  }, [companyBenefitId, companyAuthStatus, checkAuthorization])

  const _handleConfirm = async () => {
    try {
      const response = await fetch('/api/auth/me')
      setIsSignedIn(response.ok)
    } catch {
      setIsSignedIn(false)
    }
  }





  const handleVerification = async () => {
    if (!isSignedIn) {
      // Redirect to sign in or show modal
      window.location.href = '/sign-in'
      return
    }

    await submitVerification('confirmed', '')
  }





  // If user is not authorized, return null - counts will be shown in the benefit card
  if (!isLoadingAuth && authStatus && !authStatus.authorized) {
    return null
  }

  return (
    <div className="bg-gray-50 border rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <div>
          <h4 className="font-medium text-gray-900">Verify this benefit</h4>
          <p className="text-sm text-gray-600">
            Does {companyName} actually offer {benefitName}?
          </p>
        </div>
        <MessageSquare className="w-5 h-5 text-gray-500" />
      </div>

      {/* Authorization Status and Action Buttons */}
      {isLoadingAuth ? (
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
          <span>Checking authorization...</span>
        </div>
      ) : authStatus?.authorized ? (
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Button
              size="sm"
              onClick={() => handleVerification()}
              disabled={isSubmitting}
              className="flex items-center space-x-1 bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="w-4 h-4" />
              <span>Confirm</span>
            </Button>
          </div>
          <p className="text-xs text-green-600">
            ✓ {authStatus.message}
          </p>
        </div>
      ) : null}
    </div>
  )
}
