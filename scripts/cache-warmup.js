#!/usr/bin/env node

// Cache warmup script for PostgreSQL-based caching system
// Pre-loads frequently accessed data into cache

import { config } from 'dotenv'
import { warmUpCache } from '../src/lib/postgresql-cache.ts'

// Load environment variables
config({ path: '.env.local' })

async function main() {
  console.log('🔥 Starting cache warmup...\n')
  
  try {
    await warmUpCache()
    console.log('✅ Cache warmup completed successfully')
    process.exit(0)
  } catch (error) {
    console.error('❌ Cache warmup failed:', error.message)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️  Cache warmup interrupted')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️  Cache warmup terminated')
  process.exit(1)
})

main()
