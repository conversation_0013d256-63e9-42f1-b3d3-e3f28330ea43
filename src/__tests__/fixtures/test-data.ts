/**
 * Test Data Fixtures and Utilities
 * Centralized test data management for consistent testing
 */

export interface TestUser {
  id: string
  email: string
  firstName: string
  lastName: string
  role: 'user' | 'admin' | 'super_admin'
  companyDomain?: string
  isPremium: boolean
}

export interface TestCompany {
  id: string
  name: string
  domain: string
  industry: string
  size: string
  verificationStatus: 'verified' | 'pending' | 'unverified'
  description?: string
}

export interface TestBenefit {
  id: string
  name: string
  description: string
  categoryId: string
  icon: string
}

export interface TestCompanyBenefit {
  id: string
  companyId: string
  benefitId: string
  isVerified: boolean
  verifiedBy?: string
}

// Test Users
export const testUsers: TestUser[] = [
  {
    id: 'test-user-1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User1',
    role: 'user',
    companyDomain: 'testcorp.test',
    isPremium: false,
  },
  {
    id: 'test-user-2',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User2',
    role: 'user',
    companyDomain: 'testind.test',
    isPremium: true,
  },
  {
    id: 'test-admin-1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'Admin',
    role: 'admin',
    isPremium: true,
  },
  {
    id: 'test-super-admin',
    email: '<EMAIL>',
    firstName: 'Super',
    lastName: 'Admin',
    role: 'super_admin',
    isPremium: true,
  },
]

// Test Companies
export const testCompanies: TestCompany[] = [
  {
    id: 'test-company-1',
    name: 'Test Corp',
    domain: 'testcorp.test',
    industry: 'Technology',
    size: '100-500',
    verificationStatus: 'verified',
    description: 'Leading technology company for testing',
  },
  {
    id: 'test-company-2',
    name: 'Test Industries',
    domain: 'testind.test',
    industry: 'Manufacturing',
    size: '500-1000',
    verificationStatus: 'verified',
    description: 'Manufacturing company for testing',
  },
  {
    id: 'test-company-3',
    name: 'Test Startup',
    domain: 'startup.test',
    industry: 'Technology',
    size: '10-50',
    verificationStatus: 'pending',
    description: 'Innovative startup for testing',
  },
]

// Test Benefits
export const testBenefits: TestBenefit[] = [
  {
    id: 'test-benefit-1',
    name: 'Test Health Insurance',
    description: 'Comprehensive health coverage for testing',
    categoryId: 'test-cat-1',
    icon: '🏥',
  },
  {
    id: 'test-benefit-2',
    name: 'Test Remote Work',
    description: 'Flexible remote work policy for testing',
    categoryId: 'test-cat-2',
    icon: '🏠',
  },
  {
    id: 'test-benefit-3',
    name: 'Test Dental Coverage',
    description: 'Dental insurance for testing',
    categoryId: 'test-cat-1',
    icon: '🦷',
  },
  {
    id: 'test-benefit-4',
    name: 'Test Stock Options',
    description: 'Employee stock options for testing',
    categoryId: 'test-cat-3',
    icon: '📈',
  },
  {
    id: 'test-benefit-5',
    name: 'Test Gym Membership',
    description: 'Fitness center membership for testing',
    categoryId: 'test-cat-1',
    icon: '💪',
  },
]

// Test Company Benefits
export const testCompanyBenefits: TestCompanyBenefit[] = [
  {
    id: 'test-cb-1',
    companyId: 'test-company-1',
    benefitId: 'test-benefit-1',
    isVerified: true,
    verifiedBy: 'test-admin-1',
  },
  {
    id: 'test-cb-2',
    companyId: 'test-company-1',
    benefitId: 'test-benefit-2',
    isVerified: true,
    verifiedBy: 'test-admin-1',
  },
  {
    id: 'test-cb-3',
    companyId: 'test-company-2',
    benefitId: 'test-benefit-1',
    isVerified: false,
  },
  {
    id: 'test-cb-4',
    companyId: 'test-company-2',
    benefitId: 'test-benefit-3',
    isVerified: true,
    verifiedBy: 'test-admin-1',
  },
]

// E2E Test Data (separate namespace)
export const e2eUsers: TestUser[] = [
  {
    id: 'e2e-user-1',
    email: 'user1@techcorp.e2e',
    firstName: 'John',
    lastName: 'Doe',
    role: 'user',
    companyDomain: 'techcorp.e2e',
    isPremium: false,
  },
  {
    id: 'e2e-user-2',
    email: 'user2@industries.e2e',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'user',
    companyDomain: 'industries.e2e',
    isPremium: true,
  },
  {
    id: 'e2e-admin-1',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    isPremium: true,
  },
  {
    id: 'e2e-super-admin',
    email: '<EMAIL>',
    firstName: 'Super',
    lastName: 'Admin',
    role: 'super_admin',
    isPremium: true,
  },
]

export const e2eCompanies: TestCompany[] = [
  {
    id: 'e2e-company-1',
    name: 'E2E Tech Corp',
    domain: 'techcorp.e2e',
    industry: 'Technology',
    size: '100-500',
    verificationStatus: 'verified',
    description: 'Leading technology company for E2E testing',
  },
  {
    id: 'e2e-company-2',
    name: 'E2E Industries',
    domain: 'industries.e2e',
    industry: 'Manufacturing',
    size: '500-1000',
    verificationStatus: 'verified',
    description: 'Manufacturing company for E2E testing',
  },
  {
    id: 'e2e-company-3',
    name: 'E2E Startup',
    domain: 'startup.e2e',
    industry: 'Technology',
    size: '10-50',
    verificationStatus: 'pending',
    description: 'Innovative startup for E2E testing',
  },
]

// Utility functions for test data management
export class TestDataManager {
  static getUser(id: string, dataset: 'test' | 'e2e' = 'test'): TestUser | undefined {
    const users = dataset === 'e2e' ? e2eUsers : testUsers
    return users.find(user => user.id === id)
  }

  static getCompany(id: string, dataset: 'test' | 'e2e' = 'test'): TestCompany | undefined {
    const companies = dataset === 'e2e' ? e2eCompanies : testCompanies
    return companies.find(company => company.id === id)
  }

  static getBenefit(id: string): TestBenefit | undefined {
    return testBenefits.find(benefit => benefit.id === id)
  }

  static getUsersByCompany(domain: string, dataset: 'test' | 'e2e' = 'test'): TestUser[] {
    const users = dataset === 'e2e' ? e2eUsers : testUsers
    return users.filter(user => user.companyDomain === domain)
  }

  static getCompanyBenefits(companyId: string): TestCompanyBenefit[] {
    return testCompanyBenefits.filter(cb => cb.companyId === companyId)
  }

  static getVerifiedBenefits(companyId: string): TestCompanyBenefit[] {
    return testCompanyBenefits.filter(cb => cb.companyId === companyId && cb.isVerified)
  }

  static getPendingBenefits(companyId: string): TestCompanyBenefit[] {
    return testCompanyBenefits.filter(cb => cb.companyId === companyId && !cb.isVerified)
  }

  static createMockSession(userId: string): string {
    return `mock-session-${userId}-${Date.now()}`
  }

  static createMockAuthHeaders(sessionToken: string) {
    return {
      'Cookie': `session=${sessionToken}`,
      'Content-Type': 'application/json',
    }
  }

  static generateTestEmail(prefix: string, domain: string = 'test.example'): string {
    return `${prefix}-${Date.now()}@${domain}`
  }

  static generateTestId(prefix: string): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

// Mock API responses
export const mockApiResponses = {
  benefits: {
    success: {
      benefits: testBenefits,
    },
    empty: {
      benefits: [],
    },
  },
  companies: {
    success: {
      companies: testCompanies,
      total: testCompanies.length,
      page: 1,
      limit: 10,
    },
    empty: {
      companies: [],
      total: 0,
      page: 1,
      limit: 10,
    },
  },
  user: {
    authenticated: testUsers[0],
    admin: testUsers[2],
    unauthenticated: null,
  },
}

// Test scenarios for different user types
export const testScenarios = {
  regularUser: {
    user: testUsers[0],
    company: testCompanies[0],
    availableBenefits: [testBenefits[2], testBenefits[3], testBenefits[4]],
    companyBenefits: [testBenefits[0], testBenefits[1]],
  },
  premiumUser: {
    user: testUsers[1],
    company: testCompanies[1],
    availableBenefits: [testBenefits[0], testBenefits[1], testBenefits[4]],
    companyBenefits: [testBenefits[2]],
  },
  admin: {
    user: testUsers[2],
    allCompanies: testCompanies,
    allBenefits: testBenefits,
    pendingVerifications: testCompanyBenefits.filter(cb => !cb.isVerified),
  },
}
