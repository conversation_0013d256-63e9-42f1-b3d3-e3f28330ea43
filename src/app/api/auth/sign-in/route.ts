import { NextRequest, NextResponse } from 'next/server'
import { createSignInMagicLink, checkRateLimit, createSignInMagicLinkEmail } from '@/lib/magic-link-auth'
import { sendEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email } = body

    console.log('🔐 Magic link sign in request:', { email })

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Check rate limit
    const isAllowed = await checkRateLimit(email)
    if (!isAllowed) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    try {
      // Create magic link token
      const token = await createSignInMagicLink(email)

      // Send magic link email
      const emailOptions = createSignInMagicLinkEmail(email, token)
      await sendEmail(emailOptions)

      console.log('✅ Magic link sent:', email)

      return NextResponse.json({
        success: true,
        message: 'Magic link sent! Please check your email and click the link to sign in.'
      })

    } catch (error) {
      if (error instanceof Error && error.message === 'No account found with this email address') {
        return NextResponse.json(
          { error: 'No account found with this email address. Please sign up first.' },
          { status: 404 }
        )
      }
      throw error
    }

  } catch (error) {
    console.error('Magic link sign in error:', error)

    return NextResponse.json(
      { error: 'Failed to send magic link' },
      { status: 500 }
    )
  }
}
