import Link from 'next/link'
import { MapPin, Users, Building2, CheckCircle } from 'lucide-react'
import { selectBenefitsForOverview } from '@/lib/utils'
import type { Company, CompanyBenefit, CompanyLocation } from '@/types/database'

interface CompanyWithBenefits extends Company {
  company_benefits?: CompanyBenefit[]
  locations?: CompanyLocation[]
}

interface CompanyCardProps {
  company: CompanyWithBenefits
  variant?: 'default' | 'saved'
}

export function CompanyCard({ company, variant = 'default' }: CompanyCardProps) {
  const benefits = company.company_benefits || []
  const hasBenefitsData = benefits.length > 0
  const isSavedVariant = variant === 'saved'

  // Select benefits for display using the new logic
  const displayedBenefits = selectBenefitsForOverview(benefits, 5)
  const remainingBenefitsCount = benefits.length - displayedBenefits.length

  const getSizeLabel = (size: string) => {
    const labels = {
      startup: 'Startup',
      small: 'Small (1-50)',
      medium: 'Medium (51-200)',
      large: 'Large (201-1000)',
      enterprise: 'Enterprise (1000+)'
    }
    return labels[size as keyof typeof labels] || size
  }

  // Get location display text
  const getLocationDisplay = () => {
    const locations = company.locations || []

    if (locations.length === 0) {
      // No location data available
      return 'Location not specified'
    }

    // Find primary location first, then headquarters, then first location
    const primaryLocation = locations.find(loc => loc.is_primary) ||
                           locations.find(loc => loc.is_headquarters) ||
                           locations[0]

    if (locations.length === 1) {
      return primaryLocation.location_normalized || primaryLocation.location_raw
    }

    // Multiple locations - show primary + count
    const additionalCount = locations.length - 1
    return `${primaryLocation.location_normalized || primaryLocation.location_raw} +${additionalCount} more`
  }

  return (
    <Link href={`/companies/${company.id}`} className="block w-full h-full">
      <div className={`w-full bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow p-4 sm:p-6 cursor-pointer overflow-hidden ${isSavedVariant ? 'h-full flex flex-col' : ''}`}>
        {/* Header Section */}
        <div className={`flex flex-col space-y-3 mb-4 ${isSavedVariant ? 'flex-shrink-0' : ''}`}>
          <div className="flex items-start space-x-3 w-full">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Building2 className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
            </div>
            <div className="min-w-0 flex-1 overflow-hidden">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 break-words leading-tight">
                  {isSavedVariant && company.name.length > 23
                    ? `${company.name.substring(0, 23)}...`
                    : company.name}
                </h3>
                {!isSavedVariant && (
                  <span className="text-xs sm:text-sm text-gray-700 bg-gray-100 px-2 py-1 rounded flex-shrink-0 self-start" title={company.industry}>
                    {company.industry}
                  </span>
                )}
              </div>

              {/* Industry for saved variant - shown below company name */}
              {isSavedVariant && (
                <div className="mt-1">
                  <span className="text-xs text-gray-700 bg-gray-100 px-2 py-1 rounded inline-block" title={company.industry}>
                    {company.industry}
                  </span>
                </div>
              )}

              <div className={`text-xs sm:text-sm text-gray-700 mt-1 ${isSavedVariant ? 'space-y-1' : 'flex flex-col space-y-1'}`}>
                <div className="flex items-center">
                  <MapPin className="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" />
                  <span className="break-words">{getLocationDisplay()}</span>
                </div>
                <div className="flex items-center">
                  <Users className="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" />
                  <span className="break-words">{getSizeLabel(company.size)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Description Section */}
        {company.description && (
          <div className={`mb-3 sm:mb-4 ${isSavedVariant ? 'flex-shrink-0' : ''}`}>
            <p className={`text-gray-700 text-xs sm:text-sm ${isSavedVariant ? 'line-clamp-3' : 'line-clamp-2'}`}>
              {company.description}
            </p>
          </div>
        )}

        {/* Spacer for saved variant to push content to bottom while leaving space for overlay */}
        {isSavedVariant && <div className="flex-1 min-h-0 pb-8"></div>}

        {/* Benefits Section - Only show for default variant */}
        {!isSavedVariant && hasBenefitsData ? (
          <div className="mt-3 sm:mt-4 w-full overflow-hidden">
            <h4 className="text-xs sm:text-sm font-medium text-gray-900 mb-2">Benefits:</h4>
            <div className="flex flex-wrap gap-1 sm:gap-1.5 w-full">
              {displayedBenefits.map((companyBenefit) => (
                <span
                  key={companyBenefit.id}
                  className={`inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium max-w-[calc(100%-0.25rem)] ${
                    companyBenefit.is_verified
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {companyBenefit.benefit?.icon && (
                    <span className="mr-1 flex-shrink-0 text-xs">{companyBenefit.benefit.icon}</span>
                  )}
                  <span className="truncate min-w-0">{companyBenefit.benefit?.name}</span>
                  {companyBenefit.is_verified && (
                    <CheckCircle className="w-2.5 h-2.5 sm:w-3 sm:h-3 ml-1 flex-shrink-0" />
                  )}
                </span>
              ))}
              {remainingBenefitsCount > 0 && (
                <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs text-gray-700 bg-gray-50 flex-shrink-0">
                  +{remainingBenefitsCount} more
                </span>
              )}
            </div>
          </div>
        ) : null}
      </div>
    </Link>
  )
}
