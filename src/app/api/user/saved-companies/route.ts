import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logger } from '@/lib/logger'

/**
 * GET /api/user/saved-companies
 * Get current user's saved companies
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's saved companies with company details
    const result = await query(
      `SELECT
        sc.id as saved_id,
        sc.created_at as saved_at,
        c.id,
        c.name,
        c.domain,
        c.industry,
        c.size,
        c.description,
        COALESCE(benefits_agg.company_benefits, '[]'::json) as company_benefits
       FROM saved_companies sc
       JOIN companies c ON sc.company_id = c.id
       LEFT JOIN (
         SELECT 
           cb.company_id,
           json_agg(
             json_build_object(
               'id', b.id,
               'name', b.name,
               'description', b.description,
               'category_id', b.category_id,
               'icon', b.icon,
               'is_verified', cb.is_verified
             )
           ) as company_benefits
         FROM company_benefits cb
         JOIN benefits b ON cb.benefit_id = b.id
         WHERE cb.is_verified = true
         GROUP BY cb.company_id
       ) benefits_agg ON c.id = benefits_agg.company_id
       WHERE sc.user_id = $1 
       ORDER BY sc.created_at DESC`,
      [user.id]
    )

    logger.info('User saved companies retrieved', {
      userId: user.id,
      companyCount: result.rows.length
    })

    return NextResponse.json({ savedCompanies: result.rows })
  } catch (error) {
    logger.error('Error retrieving user saved companies', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/user/saved-companies
 * Save a company for the current user
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { companyId } = body

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Check if company exists
    const companyResult = await query(
      'SELECT id, name FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Try to save the company, handle duplicates gracefully
    try {
      console.log('Attempting to save company:', { userId: user.id, companyId })

      const result = await query(
        `INSERT INTO saved_companies (user_id, company_id)
         VALUES ($1, $2)
         ON CONFLICT (user_id, company_id) DO NOTHING
         RETURNING id, created_at`,
        [user.id, companyId]
      )

      console.log('Insert result:', { rowCount: result.rows.length })

      // If no rows returned, it means the company was already saved
      if (result.rows.length === 0) {
        console.log('Company already saved, returning 409')
        return NextResponse.json(
          { error: 'Company already saved' },
          { status: 409 }
        )
      }

      const savedCompany = result.rows[0]

      logger.info('Company saved by user', {
        userId: user.id,
        companyId,
        savedId: savedCompany.id
      })

      return NextResponse.json({
        success: true,
        savedCompany: {
          id: savedCompany.id,
          companyId,
          savedAt: savedCompany.created_at
        }
      })
    } catch (insertError) {
      console.error('Error saving company:', insertError)
      return NextResponse.json(
        { error: 'Failed to save company' },
        { status: 500 }
      )
    }
  } catch (error) {
    logger.error('Error saving company for user', { error })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
