#!/usr/bin/env node

// Cache statistics script for PostgreSQL-based caching system
// Shows cache performance metrics and statistics

import { config } from 'dotenv'
import { getCacheStats } from '../src/lib/postgresql-cache.ts'

// Load environment variables
config({ path: '.env.local' })

async function main() {
  console.log('📊 Fetching cache statistics...\n')

  try {
    const stats = await getCacheStats()

    console.log('🗄️  Cache Statistics:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log(`📈 Total Cache Entries: ${stats.totalCacheEntries}`)
    console.log(`⏰ Expired Entries: ${stats.expiredEntries}`)

    if (stats.cacheHitRate !== undefined) {
      console.log(`🎯 Cache Hit Rate: ${(stats.cacheHitRate * 100).toFixed(2)}%`)
    }

    if (stats.oldestEntry) {
      console.log(`📅 Oldest Entry: ${stats.oldestEntry}`)
    }

    if (stats.newestEntry) {
      console.log(`🆕 Newest Entry: ${stats.newestEntry}`)
    }

    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    console.log('✅ Cache statistics retrieved successfully')

    process.exit(0)
  } catch (error) {
    console.error('❌ Failed to get cache statistics:', error.message)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️  Cache stats interrupted')
  process.exit(1)
})

process.on('SIGTERM', () => {
  console.log('\n⚠️  Cache stats terminated')
  process.exit(1)
})

main()