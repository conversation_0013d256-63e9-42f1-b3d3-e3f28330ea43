import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/local-auth'
import { setCSRFToken } from '@/lib/csrf'

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Generate CSRF token for the user's session
    const sessionToken = request.cookies.get('session_token')?.value
    if (!sessionToken) {
      return NextResponse.json(
        { error: 'No session found' },
        { status: 401 }
      )
    }

    const csrfToken = await setCSRFToken(sessionToken)

    return NextResponse.json({
      csrfToken,
      expiresIn: 3600 // 1 hour
    })

  } catch (error) {
    console.error('Error generating CSRF token:', error)
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    )
  }
}
