#!/bin/bash

# Kubernetes Deployment Script for WorkWell

set -e

# Configuration
NAMESPACE="workwell"
IMAGE_NAME="workwell"
REGISTRY="${DOCKER_REGISTRY:-your-registry}"
TAG="${IMAGE_TAG:-latest}"
FULL_IMAGE="${REGISTRY}/${IMAGE_NAME}:${TAG}"

echo "🚀 Deploying WorkWell to Kubernetes..."
echo "📦 Image: ${FULL_IMAGE}"
echo "🏷️  Namespace: ${NAMESPACE}"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

# Build and push Docker image
echo "🔨 Building Docker image..."
docker build -t "${FULL_IMAGE}" .

echo "📤 Pushing Docker image to registry..."
docker push "${FULL_IMAGE}"

# Update the image in the deployment manifest
echo "📝 Updating deployment manifest with new image..."
sed -i.bak "s|image: your-registry/workwell:latest|image: ${FULL_IMAGE}|g" k8s/workwell.yaml

# Apply Kubernetes manifests
echo "🎯 Applying Kubernetes manifests..."

# Create namespace
kubectl apply -f k8s/namespace.yaml

# Apply configuration and secrets
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml

# Deploy PostgreSQL
echo "🐘 Deploying PostgreSQL..."
kubectl apply -f k8s/postgres.yaml

# Redis no longer needed - using PostgreSQL for caching
echo "✅ Cache configured (PostgreSQL-based)"

# Wait for database to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
kubectl wait --for=condition=ready pod -l app=postgres -n ${NAMESPACE} --timeout=300s



# Deploy the application
echo "🌐 Deploying WorkWell application..."
kubectl apply -f k8s/workwell.yaml

# Wait for application to be ready
echo "⏳ Waiting for WorkWell application to be ready..."
kubectl wait --for=condition=ready pod -l app=workwell -n ${NAMESPACE} --timeout=300s

# Restore the original manifest
mv k8s/workwell.yaml.bak k8s/workwell.yaml

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Useful commands:"
echo "   View pods: kubectl get pods -n ${NAMESPACE}"
echo "   View services: kubectl get services -n ${NAMESPACE}"
echo "   View logs: kubectl logs -f deployment/workwell -n ${NAMESPACE}"
echo "   Port forward: kubectl port-forward service/workwell 3000:80 -n ${NAMESPACE}"
echo ""
echo "🌐 If you configured an ingress, your application should be available at your configured domain."
echo "🔧 Otherwise, use port forwarding to access the application locally."
